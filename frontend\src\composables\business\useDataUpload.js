/**
 * 数据上传业务逻辑Composable
 * 封装文件上传、数据预览、验证和提交等功能
 */
import { ref, computed, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRulesStore } from '@/stores/rules'

/**
 * 数据上传主要功能
 */
export function useDataUpload() {
  const rulesStore = useRulesStore()

  // 上传状态
  const uploadState = reactive({
    isUploading: false,
    isProcessing: false,
    isSubmitting: false,
    progress: 0,
    currentStep: 1, // 1: 选择文件, 2: 数据预览, 3: 确认提交
    totalSteps: 3
  })

  // 文件相关状态
  const fileState = reactive({
    selectedFile: null,
    fileName: '',
    fileSize: 0,
    fileType: '',
    lastModified: null
  })

  // 数据相关状态
  const dataState = reactive({
    rawData: [],
    processedData: [],
    validationErrors: [],
    summary: {
      totalRows: 0,
      validRows: 0,
      errorRows: 0,
      columns: []
    }
  })

  // 计算属性
  const canProceedToPreview = computed(() => {
    return fileState.selectedFile && !uploadState.isUploading
  })

  const canSubmitData = computed(() => {
    return dataState.processedData.length > 0 && 
           dataState.validationErrors.length === 0 && 
           !uploadState.isSubmitting
  })

  const uploadProgress = computed(() => {
    return Math.round((uploadState.currentStep / uploadState.totalSteps) * 100)
  })

  const hasValidationErrors = computed(() => {
    return dataState.validationErrors.length > 0
  })

  const dataQualityScore = computed(() => {
    if (dataState.summary.totalRows === 0) return 0
    return Math.round((dataState.summary.validRows / dataState.summary.totalRows) * 100)
  })

  // 文件选择处理
  const handleFileSelect = (file) => {
    if (!file) return false

    // 文件类型验证
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv'
    ]

    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('请选择Excel文件(.xlsx, .xls)或CSV文件')
      return false
    }

    // 文件大小验证 (10MB限制)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过10MB')
      return false
    }

    // 更新文件状态
    fileState.selectedFile = file
    fileState.fileName = file.name
    fileState.fileSize = file.size
    fileState.fileType = file.type
    fileState.lastModified = new Date(file.lastModified)

    ElMessage.success('文件选择成功')
    return true
  }

  // 文件上传和解析
  const uploadAndParseFile = async () => {
    if (!fileState.selectedFile) {
      ElMessage.error('请先选择文件')
      return false
    }

    uploadState.isUploading = true
    uploadState.progress = 0

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        if (uploadState.progress < 90) {
          uploadState.progress += 10
        }
      }, 100)

      // 解析文件内容
      const fileContent = await parseFileContent(fileState.selectedFile)
      
      clearInterval(progressInterval)
      uploadState.progress = 100

      // 处理解析结果
      dataState.rawData = fileContent.data
      dataState.summary = fileContent.summary
      
      // 数据验证
      await validateData()

      uploadState.currentStep = 2
      ElMessage.success('文件解析成功')
      return true

    } catch (error) {
      console.error('文件解析失败:', error)
      ElMessage.error('文件解析失败: ' + error.message)
      return false
    } finally {
      uploadState.isUploading = false
    }
  }

  // 解析文件内容
  const parseFileContent = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = e.target.result
          let parsedData = []
          let columns = []

          if (file.type.includes('csv')) {
            // CSV解析
            const lines = data.split('\n').filter(line => line.trim())
            if (lines.length === 0) {
              throw new Error('文件内容为空')
            }

            columns = lines[0].split(',').map(col => col.trim())
            parsedData = lines.slice(1).map((line, index) => {
              const values = line.split(',').map(val => val.trim())
              const row = { _rowIndex: index + 2 } // Excel行号从2开始
              columns.forEach((col, colIndex) => {
                row[col] = values[colIndex] || ''
              })
              return row
            })
          } else {
            // Excel解析 (这里需要使用xlsx库，简化处理)
            // 实际项目中应该使用xlsx库来解析Excel文件
            throw new Error('Excel文件解析需要xlsx库支持')
          }

          const summary = {
            totalRows: parsedData.length,
            validRows: parsedData.length,
            errorRows: 0,
            columns: columns
          }

          resolve({ data: parsedData, summary })
        } catch (error) {
          reject(error)
        }
      }

      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }

      if (file.type.includes('csv')) {
        reader.readAsText(file, 'UTF-8')
      } else {
        reader.readAsBinaryString(file)
      }
    })
  }

  // 数据验证
  const validateData = async () => {
    uploadState.isProcessing = true
    dataState.validationErrors = []

    try {
      const errors = []
      const processedData = []

      dataState.rawData.forEach((row, index) => {
        const rowErrors = []

        // 基础验证规则
        Object.keys(row).forEach(key => {
          if (key === '_rowIndex') return

          const value = row[key]
          
          // 必填字段验证
          if (!value || value.toString().trim() === '') {
            rowErrors.push({
              row: index + 1,
              column: key,
              error: '字段不能为空',
              value: value
            })
          }
        })

        if (rowErrors.length > 0) {
          errors.push(...rowErrors)
        } else {
          processedData.push(row)
        }
      })

      dataState.validationErrors = errors
      dataState.processedData = processedData
      dataState.summary.validRows = processedData.length
      dataState.summary.errorRows = errors.length

    } catch (error) {
      console.error('数据验证失败:', error)
      ElMessage.error('数据验证失败')
    } finally {
      uploadState.isProcessing = false
    }
  }

  // 提交数据
  const submitData = async (ruleKey) => {
    if (!canSubmitData.value) {
      ElMessage.error('数据验证未通过，无法提交')
      return false
    }

    try {
      await ElMessageBox.confirm(
        `确认提交 ${dataState.summary.validRows} 条有效数据？`,
        '确认提交',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return false
    }

    uploadState.isSubmitting = true

    try {
      const submissionData = {
        fileName: fileState.fileName,
        fileSize: fileState.fileSize,
        uploadTime: new Date().toISOString(),
        data: dataState.processedData,
        summary: dataState.summary
      }

      await rulesStore.submitRuleData(ruleKey, submissionData)
      
      uploadState.currentStep = 3
      ElMessage.success('数据提交成功')
      return true

    } catch (error) {
      console.error('数据提交失败:', error)
      ElMessage.error('数据提交失败')
      return false
    } finally {
      uploadState.isSubmitting = false
    }
  }

  // 重置状态
  const resetUpload = () => {
    // 重置上传状态
    Object.assign(uploadState, {
      isUploading: false,
      isProcessing: false,
      isSubmitting: false,
      progress: 0,
      currentStep: 1
    })

    // 重置文件状态
    Object.assign(fileState, {
      selectedFile: null,
      fileName: '',
      fileSize: 0,
      fileType: '',
      lastModified: null
    })

    // 重置数据状态
    Object.assign(dataState, {
      rawData: [],
      processedData: [],
      validationErrors: [],
      summary: {
        totalRows: 0,
        validRows: 0,
        errorRows: 0,
        columns: []
      }
    })
  }

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return {
    // 状态
    uploadState,
    fileState,
    dataState,

    // 计算属性
    canProceedToPreview,
    canSubmitData,
    uploadProgress,
    hasValidationErrors,
    dataQualityScore,

    // 方法
    handleFileSelect,
    uploadAndParseFile,
    validateData,
    submitData,
    resetUpload,
    formatFileSize
  }
}
