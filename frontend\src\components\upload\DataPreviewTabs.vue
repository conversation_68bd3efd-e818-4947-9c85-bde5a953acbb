<template>
  <div class="data-preview-tabs">
    <div class="preview-header">
      <h3 class="section-title">数据预览</h3>
      <div class="preview-summary">
        <span class="summary-item">
          <i class="el-icon-check text-success"></i>
          有效数据：{{ validRows.length }} 条
        </span>
        <span class="summary-item">
          <i class="el-icon-warning text-warning"></i>
          无效数据：{{ invalidRows.length }} 条
        </span>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="preview-tabs">
      <el-tab-pane 
        :label="`有效数据 (${validRows.length})`" 
        name="valid"
      >
        <div class="tab-content">
          <el-table 
            :data="validRows" 
            stripe 
            border
            max-height="400"
            class="data-table"
          >
            <el-table-column
              v-for="column in displayColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane 
        :label="`无效数据 (${invalidRows.length})`" 
        name="invalid"
      >
        <div class="tab-content">
          <el-table 
            :data="invalidRows" 
            stripe 
            border
            max-height="400"
            class="data-table"
          >
            <el-table-column
              prop="rowNumber"
              label="行号"
              width="80"
            />
            <el-table-column
              prop="error"
              label="错误信息"
              width="200"
              show-overflow-tooltip
            />
            <el-table-column
              v-for="column in displayColumns"
              :key="column.prop"
              :prop="`data.${column.prop}`"
              :label="column.label"
              show-overflow-tooltip
            />
            <el-table-column
              label="操作"
              width="120"
              fixed="right"
            >
              <template #default="scope">
                <div class="item-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleRevalidate(scope.row)"
                  >
                    重新校验
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Props
const props = defineProps({
  validRows: {
    type: Array,
    default: () => []
  },
  invalidRows: {
    type: Array,
    default: () => []
  },
  displayColumns: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['revalidate-row'])

// Data
const activeTab = ref('valid')

// Methods
const handleRevalidate = (row) => {
  emit('revalidate-row', row)
}
</script>

<style scoped>
.data-preview-tabs {
  margin-top: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.preview-summary {
  display: flex;
  gap: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.preview-tabs {
  background: white;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.tab-content {
  padding: 16px;
}

.data-table {
  width: 100%;
}

.item-actions {
  display: flex;
  gap: 8px;
}

:deep(.el-tabs__header) {
  margin: 0;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 16px;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 48px;
  line-height: 48px;
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background-color: #fafafa;
}
</style>
