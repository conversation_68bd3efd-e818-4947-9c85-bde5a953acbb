import { ref, computed, watch, nextTick } from 'vue'
import { useRuleDetailsStore } from '@/stores/ruleDetails'
import { useRulesStore } from '@/stores/rules'
import { ElMessage, ElMessageBox } from 'element-plus'
import { debounce } from 'lodash-es'

/**
 * 规则明细管理 Composable
 * 提供规则明细的完整管理功能，包括 CRUD、搜索、过滤、分页等
 */
export function useRuleDetailsManagement(ruleKey) {
  // Store 实例
  const ruleDetailsStore = useRuleDetailsStore()
  const rulesStore = useRulesStore()

  // 从 Store 获取状态
  const {
    detailsList,
    currentDetail,
    selectedDetails,
    pagination,
    filters,
    operationStatus,
    loading,
    detailLoading,
    batchLoading,
    searchLoading,
    isLoading,
    paginationInfo,
    activeFilters,
    detailsCount,
    hasDetails,
    selectedCount,
    hasSelected
  } = ruleDetailsStore

  // 本地状态
  const currentRuleKey = ref(ruleKey || '')
  const searchKeyword = ref('')
  const isInitialized = ref(false)

  // ==================== 计算属性 ====================

  // 当前规则信息
  const currentRuleInfo = computed(() => {
    if (!currentRuleKey.value) return null
    return rulesStore.getRuleByKey(currentRuleKey.value)
  })

  // 是否有搜索条件
  const hasSearchConditions = computed(() => {
    return searchKeyword.value.trim() !== '' ||
      filters.status !== null ||
      filters.errorLevel1 !== null ||
      filters.errorLevel2 !== null ||
      filters.ruleCategory !== null
  })

  // 表格列配置
  const tableColumns = computed(() => [
    { prop: 'rule_detail_id', label: '规则ID', width: 120, fixed: 'left' },
    { prop: 'rule_name', label: '规则名称', minWidth: 200, showOverflowTooltip: true },
    { prop: 'error_level_1', label: '一级错误', width: 120 },
    { prop: 'error_level_2', label: '二级错误', width: 120 },
    { prop: 'rule_category', label: '规则类别', width: 120 },
    { prop: 'status', label: '状态', width: 100 },
    { prop: 'created_at', label: '创建时间', width: 160 },
    { prop: 'actions', label: '操作', width: 200, fixed: 'right' }
  ])

  // ==================== 初始化和数据加载 ====================

  /**
   * 初始化管理器
   * @param {string} newRuleKey - 规则键
   */
  const initialize = async (newRuleKey) => {
    if (newRuleKey) {
      currentRuleKey.value = newRuleKey
    }

    if (!currentRuleKey.value) {
      console.warn('useRuleDetailsManagement: 缺少 ruleKey')
      return
    }

    try {
      await loadDetailsList()
      isInitialized.value = true
    } catch (error) {
      console.error('初始化规则明细管理失败:', error)
      ElMessage.error('初始化失败')
    }
  }

  /**
   * 加载明细列表
   * @param {Object} params - 查询参数
   */
  const loadDetailsList = async (params = {}) => {
    if (!currentRuleKey.value) return

    try {
      const queryParams = {
        page: pagination.page,
        page_size: pagination.pageSize,
        ...filters,
        ...params
      }

      await ruleDetailsStore.fetchDetailsList(currentRuleKey.value, queryParams)
    } catch (error) {
      console.error('加载明细列表失败:', error)
    }
  }

  /**
   * 刷新列表
   */
  const refreshList = async () => {
    await loadDetailsList({ _refresh: Date.now() })
  }

  // ==================== 搜索和过滤 ====================

  /**
   * 防抖搜索
   */
  const debouncedSearch = debounce(async (keyword) => {
    if (!currentRuleKey.value) return

    try {
      const searchParams = {
        ...filters,
        search: keyword, // 确保搜索关键词不被覆盖
        page: 1, // 搜索时重置到第一页
        page_size: pagination.pageSize
      }

      await ruleDetailsStore.searchDetails(currentRuleKey.value, searchParams)
    } catch (error) {
      console.error('搜索失败:', error)
    }
  }, 300)

  /**
   * 执行搜索
   * @param {string} keyword - 搜索关键词
   */
  const performSearch = (keyword) => {
    searchKeyword.value = keyword
    debouncedSearch(keyword)
  }

  /**
   * 清除搜索
   */
  const clearSearch = () => {
    searchKeyword.value = ''
    ruleDetailsStore.updateFilters({ search: '' })
    loadDetailsList({ page: 1 })
  }

  /**
   * 应用过滤条件
   * @param {Object} newFilters - 新的过滤条件
   */
  const applyFilters = (newFilters) => {
    ruleDetailsStore.updateFilters(newFilters)
    loadDetailsList({ page: 1 })
  }

  /**
   * 重置过滤条件
   */
  const resetFilters = () => {
    ruleDetailsStore.resetFilters()
    searchKeyword.value = ''
    loadDetailsList({ page: 1 })
  }

  // ==================== 分页管理 ====================

  /**
   * 处理分页变化
   * @param {number} page - 页码
   * @param {number} pageSize - 页面大小
   */
  const handlePageChange = (page, pageSize) => {
    ruleDetailsStore.updatePagination({ page, pageSize })
    loadDetailsList()
  }

  /**
   * 处理页面大小变化
   * @param {number} pageSize - 页面大小
   */
  const handlePageSizeChange = (pageSize) => {
    ruleDetailsStore.updatePagination({ page: 1, pageSize })
    loadDetailsList()
  }

  // ==================== CRUD 操作 ====================

  /**
   * 查看明细详情
   * @param {Object} detail - 明细对象
   */
  const viewDetail = async (detail) => {
    try {
      await ruleDetailsStore.fetchDetailById(currentRuleKey.value, detail.id)
    } catch (error) {
      console.error('获取明细详情失败:', error)
    }
  }

  /**
   * 创建新明细
   * @param {Object} detailData - 明细数据
   */
  const createDetail = async (detailData) => {
    try {
      const result = await ruleDetailsStore.createDetail(currentRuleKey.value, detailData)
      if (result) {
        await refreshList()
        // 同步更新规则状态
        await rulesStore.refreshRule(currentRuleKey.value)
      }
      return result
    } catch (error) {
      console.error('创建明细失败:', error)
      throw error
    }
  }

  /**
   * 更新明细
   * @param {Object} detail - 明细对象
   * @param {Object} updateData - 更新数据
   */
  const updateDetail = async (detail, updateData) => {
    try {
      const result = await ruleDetailsStore.updateDetail(
        currentRuleKey.value,
        detail.id,
        updateData
      )
      if (result) {
        // 同步更新规则状态
        await rulesStore.refreshRule(currentRuleKey.value)
      }
      return result
    } catch (error) {
      console.error('更新明细失败:', error)
      throw error
    }
  }

  /**
   * 删除明细
   * @param {Object} detail - 明细对象
   */
  const deleteDetail = async (detail) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除明细 "${detail.rule_name}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const result = await ruleDetailsStore.deleteDetail(currentRuleKey.value, detail.id)
      if (result) {
        await refreshList()
        // 同步更新规则状态
        await rulesStore.refreshRule(currentRuleKey.value)
      }
      return result
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除明细失败:', error)
        throw error
      }
    }
  }

  // ==================== 选择管理 ====================

  /**
   * 处理行选择变化
   * @param {Array} selection - 选中的行
   */
  const handleSelectionChange = (selection) => {
    ruleDetailsStore.selectedDetails = selection
  }

  /**
   * 切换行选择
   * @param {Object} detail - 明细对象
   */
  const toggleRowSelection = (detail) => {
    ruleDetailsStore.toggleDetailSelection(detail)
  }

  /**
   * 全选/取消全选
   * @param {boolean} selectAll - 是否全选
   */
  const handleSelectAll = (selectAll) => {
    ruleDetailsStore.toggleSelectAll(selectAll)
  }

  // ==================== 监听器 ====================

  // 监听 ruleKey 变化
  watch(() => currentRuleKey.value, async (newRuleKey) => {
    if (newRuleKey && newRuleKey !== ruleKey) {
      await initialize(newRuleKey)
    }
  }, { immediate: true })

  // ==================== 生命周期 ====================

  // 如果传入了 ruleKey，自动初始化
  if (ruleKey) {
    nextTick(() => {
      initialize(ruleKey)
    })
  }

  // ==================== 返回接口 ====================

  return {
    // 状态
    currentRuleKey,
    searchKeyword,
    isInitialized,
    detailsList,
    currentDetail,
    selectedDetails,
    pagination,
    filters,
    operationStatus,

    // 加载状态
    loading,
    detailLoading,
    batchLoading,
    searchLoading,
    isLoading,

    // 计算属性
    currentRuleInfo,
    hasSearchConditions,
    tableColumns,
    paginationInfo,
    activeFilters,
    detailsCount,
    hasDetails,
    selectedCount,
    hasSelected,

    // 初始化和数据加载
    initialize,
    loadDetailsList,
    refreshList,

    // 搜索和过滤
    performSearch,
    clearSearch,
    applyFilters,
    resetFilters,

    // 分页管理
    handlePageChange,
    handlePageSizeChange,

    // CRUD 操作
    viewDetail,
    createDetail,
    updateDetail,
    deleteDetail,

    // 选择管理
    handleSelectionChange,
    toggleRowSelection,
    handleSelectAll
  }
}
