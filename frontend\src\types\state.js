/**
 * 状态管理相关的类型定义
 */

/**
 * 异步操作状态枚举
 */
export const AsyncStates = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  RETRYING: 'retrying'
}

/**
 * 状态事件枚举
 */
export const StateEvents = {
  START: 'start',
  SUCCESS: 'success',
  ERROR: 'error',
  RETRY: 'retry',
  RESET: 'reset',
  ROLLBACK: 'rollback'
}

/**
 * 状态转换结果类型
 */
export const TransitionResult = {
  SUCCESS: 'success',
  FAILED: 'failed',
  BLOCKED: 'blocked'
}

/**
 * 状态机配置类型
 */
export const StateMachineConfig = {
  // 初始状态
  initialState: AsyncStates.IDLE,
  
  // 状态定义
  states: {},
  
  // 转换规则
  transitions: {},
  
  // 最大历史记录数量
  maxHistorySize: 50
}
