/**
 * 性能基准测试
 * 测试各种性能优化功能的效果
 */

import { describe, bench, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { 
  debounce, 
  throttle, 
  LRUCache, 
  memoize,
  PerformanceMonitor 
} from '@/utils/performanceUtils'
import { createMockDataList, createMockRule, PerformanceHelper } from '../utils/testHelpers'

// 创建大量测试数据
const SMALL_DATASET = createMockDataList(100, (index) => createMockRule({ 
  rule_key: `rule-${index}`, 
  rule_name: `规则${index}` 
}))

const MEDIUM_DATASET = createMockDataList(1000, (index) => createMockRule({ 
  rule_key: `rule-${index}`, 
  rule_name: `规则${index}` 
}))

const LARGE_DATASET = createMockDataList(10000, (index) => createMockRule({ 
  rule_key: `rule-${index}`, 
  rule_name: `规则${index}` 
}))

describe('性能基准测试', () => {
  describe('防抖节流性能测试', () => {
    bench('原生函数调用', () => {
      const fn = (x) => x * 2
      for (let i = 0; i < 1000; i++) {
        fn(i)
      }
    })

    bench('防抖函数调用', () => {
      const fn = (x) => x * 2
      const debouncedFn = debounce(fn, 100)
      for (let i = 0; i < 1000; i++) {
        debouncedFn(i)
      }
    })

    bench('节流函数调用', () => {
      const fn = (x) => x * 2
      const throttledFn = throttle(fn, 100)
      for (let i = 0; i < 1000; i++) {
        throttledFn(i)
      }
    })
  })

  describe('缓存性能测试', () => {
    bench('Map 缓存读写', () => {
      const cache = new Map()
      
      // 写入
      for (let i = 0; i < 1000; i++) {
        cache.set(`key-${i}`, `value-${i}`)
      }
      
      // 读取
      for (let i = 0; i < 1000; i++) {
        cache.get(`key-${i}`)
      }
    })

    bench('LRU 缓存读写', () => {
      const cache = new LRUCache(1000)
      
      // 写入
      for (let i = 0; i < 1000; i++) {
        cache.set(`key-${i}`, `value-${i}`)
      }
      
      // 读取
      for (let i = 0; i < 1000; i++) {
        cache.get(`key-${i}`)
      }
    })

    bench('内存化函数', () => {
      const expensiveFunction = (n) => {
        let result = 0
        for (let i = 0; i < n; i++) {
          result += i
        }
        return result
      }
      
      const memoizedFn = memoize(expensiveFunction)
      
      // 首次调用（计算）
      for (let i = 0; i < 100; i++) {
        memoizedFn(100)
      }
      
      // 后续调用（缓存）
      for (let i = 0; i < 100; i++) {
        memoizedFn(100)
      }
    })
  })

  describe('数据处理性能测试', () => {
    bench('小数据集过滤 (100项)', () => {
      SMALL_DATASET.filter(item => item.status === 'ACTIVE')
    })

    bench('中等数据集过滤 (1000项)', () => {
      MEDIUM_DATASET.filter(item => item.status === 'ACTIVE')
    })

    bench('大数据集过滤 (10000项)', () => {
      LARGE_DATASET.filter(item => item.status === 'ACTIVE')
    })

    bench('小数据集排序 (100项)', () => {
      [...SMALL_DATASET].sort((a, b) => a.rule_name.localeCompare(b.rule_name))
    })

    bench('中等数据集排序 (1000项)', () => {
      [...MEDIUM_DATASET].sort((a, b) => a.rule_name.localeCompare(b.rule_name))
    })

    bench('大数据集排序 (10000项)', () => {
      [...LARGE_DATASET].sort((a, b) => a.rule_name.localeCompare(b.rule_name))
    })
  })

  describe('JSON 序列化性能测试', () => {
    bench('小数据集 JSON.stringify (100项)', () => {
      JSON.stringify(SMALL_DATASET)
    })

    bench('中等数据集 JSON.stringify (1000项)', () => {
      JSON.stringify(MEDIUM_DATASET)
    })

    bench('大数据集 JSON.stringify (10000项)', () => {
      JSON.stringify(LARGE_DATASET)
    })

    bench('小数据集 JSON.parse (100项)', () => {
      const jsonString = JSON.stringify(SMALL_DATASET)
      JSON.parse(jsonString)
    })

    bench('中等数据集 JSON.parse (1000项)', () => {
      const jsonString = JSON.stringify(MEDIUM_DATASET)
      JSON.parse(jsonString)
    })

    bench('大数据集 JSON.parse (10000项)', () => {
      const jsonString = JSON.stringify(LARGE_DATASET)
      JSON.parse(jsonString)
    })
  })

  describe('DOM 操作性能测试', () => {
    bench('创建简单组件', () => {
      const SimpleComponent = {
        template: '<div>{{ message }}</div>',
        data() {
          return { message: 'Hello World' }
        }
      }
      
      const wrapper = mount(SimpleComponent, {
        global: {
          plugins: [createPinia()]
        }
      })
      
      wrapper.unmount()
    })

    bench('创建复杂组件', () => {
      const ComplexComponent = {
        template: `
          <div>
            <h1>{{ title }}</h1>
            <ul>
              <li v-for="item in items" :key="item.id">
                {{ item.name }} - {{ item.status }}
              </li>
            </ul>
          </div>
        `,
        data() {
          return {
            title: '规则列表',
            items: SMALL_DATASET
          }
        }
      }
      
      const wrapper = mount(ComplexComponent, {
        global: {
          plugins: [createPinia()]
        }
      })
      
      wrapper.unmount()
    })
  })

  describe('性能监控开销测试', () => {
    bench('无性能监控的函数执行', () => {
      const fn = () => {
        let sum = 0
        for (let i = 0; i < 1000; i++) {
          sum += i
        }
        return sum
      }
      
      for (let i = 0; i < 100; i++) {
        fn()
      }
    })

    bench('带性能监控的函数执行', () => {
      const monitor = new PerformanceMonitor()
      
      const fn = () => {
        monitor.mark('start')
        let sum = 0
        for (let i = 0; i < 1000; i++) {
          sum += i
        }
        monitor.measure('duration', 'start')
        return sum
      }
      
      for (let i = 0; i < 100; i++) {
        fn()
      }
    })
  })

  describe('内存使用测试', () => {
    bench('创建大量对象', () => {
      const objects = []
      for (let i = 0; i < 10000; i++) {
        objects.push({
          id: i,
          name: `Object ${i}`,
          data: new Array(100).fill(i)
        })
      }
      // 清理引用
      objects.length = 0
    })

    bench('创建大量字符串', () => {
      const strings = []
      for (let i = 0; i < 10000; i++) {
        strings.push(`This is a test string number ${i}`.repeat(10))
      }
      // 清理引用
      strings.length = 0
    })
  })

  describe('算法复杂度测试', () => {
    // O(n) 线性搜索
    bench('线性搜索 (1000项)', () => {
      const target = 'rule-500'
      MEDIUM_DATASET.find(item => item.rule_key === target)
    })

    // O(log n) 二分搜索（需要排序数组）
    bench('二分搜索 (1000项)', () => {
      const sortedData = [...MEDIUM_DATASET].sort((a, b) => a.rule_key.localeCompare(b.rule_key))
      const target = 'rule-500'
      
      let left = 0
      let right = sortedData.length - 1
      
      while (left <= right) {
        const mid = Math.floor((left + right) / 2)
        const comparison = sortedData[mid].rule_key.localeCompare(target)
        
        if (comparison === 0) {
          break
        } else if (comparison < 0) {
          left = mid + 1
        } else {
          right = mid - 1
        }
      }
    })

    // O(1) 哈希表查找
    bench('哈希表查找 (1000项)', () => {
      const hashMap = new Map()
      MEDIUM_DATASET.forEach(item => {
        hashMap.set(item.rule_key, item)
      })
      
      const target = 'rule-500'
      hashMap.get(target)
    })
  })

  describe('虚拟滚动性能测试', () => {
    bench('渲染全部项目 (1000项)', () => {
      const items = MEDIUM_DATASET
      const renderedItems = items.map(item => ({
        ...item,
        rendered: true
      }))
      expect(renderedItems.length).toBe(1000)
    })

    bench('虚拟滚动渲染 (显示50项)', () => {
      const items = MEDIUM_DATASET
      const visibleStart = 0
      const visibleEnd = 50
      const renderedItems = items.slice(visibleStart, visibleEnd).map(item => ({
        ...item,
        rendered: true
      }))
      expect(renderedItems.length).toBe(50)
    })
  })
})

/**
 * 性能基准测试工具函数
 */
export class PerformanceBenchmark {
  constructor() {
    this.results = new Map()
  }

  /**
   * 运行基准测试
   * @param {string} name - 测试名称
   * @param {Function} fn - 测试函数
   * @param {number} iterations - 迭代次数
   * @returns {Object} 测试结果
   */
  async run(name, fn, iterations = 1000) {
    const times = []
    
    // 预热
    for (let i = 0; i < 10; i++) {
      await fn()
    }
    
    // 正式测试
    for (let i = 0; i < iterations; i++) {
      const start = performance.now()
      await fn()
      const end = performance.now()
      times.push(end - start)
    }
    
    const result = this.calculateStats(times)
    this.results.set(name, result)
    
    return result
  }

  /**
   * 计算统计数据
   * @param {Array} times - 时间数组
   * @returns {Object} 统计结果
   */
  calculateStats(times) {
    const sorted = times.sort((a, b) => a - b)
    const sum = times.reduce((a, b) => a + b, 0)
    
    return {
      min: sorted[0],
      max: sorted[sorted.length - 1],
      mean: sum / times.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
      iterations: times.length
    }
  }

  /**
   * 比较两个测试结果
   * @param {string} baseline - 基准测试名称
   * @param {string} comparison - 对比测试名称
   * @returns {Object} 比较结果
   */
  compare(baseline, comparison) {
    const baseResult = this.results.get(baseline)
    const compResult = this.results.get(comparison)
    
    if (!baseResult || !compResult) {
      throw new Error('Test results not found')
    }
    
    return {
      meanImprovement: ((baseResult.mean - compResult.mean) / baseResult.mean) * 100,
      medianImprovement: ((baseResult.median - compResult.median) / baseResult.median) * 100,
      p95Improvement: ((baseResult.p95 - compResult.p95) / baseResult.p95) * 100
    }
  }

  /**
   * 获取所有测试结果
   * @returns {Object} 所有结果
   */
  getAllResults() {
    return Object.fromEntries(this.results)
  }

  /**
   * 清除所有结果
   */
  clear() {
    this.results.clear()
  }
}
