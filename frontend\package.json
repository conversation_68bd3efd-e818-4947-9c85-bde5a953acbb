{"name": "rule-management-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm run generate:types && vite", "build": "npm run generate:types && vite build", "preview": "vite preview", "build:prod": "npm run generate:types && vite build --mode production", "build:docker": "docker build -t rule-frontend .", "serve": "vite preview --host 0.0.0.0 --port 3000", "lint": "eslint src --ext .vue,.js,.ts --fix", "type-check": "vue-tsc --noEmit", "generate:types": "node scripts/generate-field-types.js", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:bench": "vitest bench", "test:components": "vitest run src/tests/components", "test:stores": "vitest run src/tests/stores", "test:api": "vitest run src/tests/api", "test:performance": "vitest run src/tests/performance", "test:startup": "node scripts/test-startup.js", "test:build": "npm run build && npm run preview"}, "dependencies": {"axios": "^1.7.2", "crypto-js": "^4.2.0", "element-plus": "^2.7.7", "file-saver": "^2.0.5", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.3", "xlsx": "^0.18.5", "@element-plus/icons-vue": "^2.3.1", "dayjs": "^1.11.10"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "vite": "^5.2.0", "vitest": "^1.6.0", "@vue/test-utils": "^2.4.6", "jsdom": "^24.1.0", "@vitest/ui": "^1.6.0", "c8": "^8.0.1", "vue-virtual-scroller": "^2.0.0-beta.8", "lodash-es": "^4.17.21"}}