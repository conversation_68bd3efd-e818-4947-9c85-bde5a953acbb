/**
 * API错误处理工具
 * 提供统一的错误处理、重试机制和用户友好的错误提示
 */

import { ElMessage, ElNotification } from 'element-plus'

/**
 * 错误类型枚举
 */
export const ErrorType = {
  NETWORK: 'NETWORK',
  VALIDATION: 'VALIDATION',
  PERMISSION: 'PERMISSION',
  SERVER: 'SERVER',
  BUSINESS: 'BUSINESS',
  UNKNOWN: 'UNKNOWN'
}

/**
 * 错误级别枚举
 */
export const ErrorLevel = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
}

/**
 * 默认错误配置
 */
const defaultErrorConfig = {
  showMessage: true,
  showNotification: false,
  enableRetry: false,
  retryCount: 3,
  retryDelay: 1000,
  logError: true
}

/**
 * 错误分类器
 * @param {Error} error - 错误对象
 * @returns {Object} 错误分类信息
 */
export function classifyError(error) {
  const { response, code, message } = error

  // 网络错误
  if (!response || code === 'NETWORK_ERROR') {
    return {
      type: ErrorType.NETWORK,
      level: ErrorLevel.ERROR,
      userMessage: '网络连接失败，请检查网络设置'
    }
  }

  const status = response.status

  // HTTP状态码分类
  switch (true) {
    case status >= 400 && status < 500:
      if (status === 401) {
        return {
          type: ErrorType.PERMISSION,
          level: ErrorLevel.WARNING,
          userMessage: '登录已过期，请重新登录'
        }
      }
      if (status === 403) {
        return {
          type: ErrorType.PERMISSION,
          level: ErrorLevel.WARNING,
          userMessage: '权限不足，无法执行此操作'
        }
      }
      if (status === 422) {
        return {
          type: ErrorType.VALIDATION,
          level: ErrorLevel.WARNING,
          userMessage: '数据验证失败，请检查输入内容'
        }
      }
      return {
        type: ErrorType.BUSINESS,
        level: ErrorLevel.WARNING,
        userMessage: response.data?.message || '请求处理失败'
      }

    case status >= 500:
      return {
        type: ErrorType.SERVER,
        level: ErrorLevel.ERROR,
        userMessage: '服务器内部错误，请稍后重试'
      }

    default:
      return {
        type: ErrorType.UNKNOWN,
        level: ErrorLevel.ERROR,
        userMessage: message || '未知错误'
      }
  }
}

/**
 * 显示错误消息
 * @param {Object} errorInfo - 错误信息
 * @param {Object} config - 显示配置
 */
export function showErrorMessage(errorInfo, config = {}) {
  const { type, level, userMessage } = errorInfo
  const { showMessage = true, showNotification = false } = config

  if (showMessage) {
    ElMessage({
      type: level === ErrorLevel.WARNING ? 'warning' : 'error',
      message: userMessage,
      duration: 5000,
      showClose: true
    })
  }

  if (showNotification) {
    ElNotification({
      type: level === ErrorLevel.WARNING ? 'warning' : 'error',
      title: '操作失败',
      message: userMessage,
      duration: 8000
    })
  }
}

/**
 * 记录错误日志
 * @param {Error} error - 原始错误
 * @param {Object} errorInfo - 错误分类信息
 * @param {Object} context - 上下文信息
 */
export function logError(error, errorInfo, context = {}) {
  const logData = {
    timestamp: new Date().toISOString(),
    error: {
      message: error.message,
      stack: error.stack,
      response: error.response?.data
    },
    errorInfo,
    context,
    userAgent: navigator.userAgent,
    url: window.location.href
  }

  // 开发环境下打印到控制台
  if (process.env.NODE_ENV === 'development') {
    console.group('🚨 API Error')
    console.error('Error:', error)
    console.log('Classification:', errorInfo)
    console.log('Context:', context)
    console.groupEnd()
  }

  // 生产环境下可以发送到错误监控服务
  if (process.env.NODE_ENV === 'production') {
    // TODO: 发送到错误监控服务 (如 Sentry)
    // sendToErrorService(logData)
  }
}

/**
 * 重试机制
 * @param {Function} apiCall - API调用函数
 * @param {Object} config - 重试配置
 * @returns {Promise} 重试结果
 */
export async function retryApiCall(apiCall, config = {}) {
  const { retryCount = 3, retryDelay = 1000 } = config
  let lastError

  for (let i = 0; i <= retryCount; i++) {
    try {
      return await apiCall()
    } catch (error) {
      lastError = error
      
      // 最后一次重试失败
      if (i === retryCount) {
        throw error
      }

      // 某些错误不需要重试
      const errorInfo = classifyError(error)
      if (errorInfo.type === ErrorType.VALIDATION || 
          errorInfo.type === ErrorType.PERMISSION) {
        throw error
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, retryDelay * (i + 1)))
    }
  }

  throw lastError
}

/**
 * API错误处理装饰器
 * @param {Function} apiCall - API调用函数
 * @param {Object} config - 错误处理配置
 * @returns {Function} 包装后的API函数
 */
export function withErrorHandling(apiCall, config = {}) {
  const finalConfig = { ...defaultErrorConfig, ...config }

  return async function(...args) {
    const context = {
      functionName: apiCall.name,
      arguments: args,
      timestamp: new Date().toISOString()
    }

    try {
      // 如果启用重试，使用重试机制
      if (finalConfig.enableRetry) {
        return await retryApiCall(() => apiCall(...args), finalConfig)
      } else {
        return await apiCall(...args)
      }
    } catch (error) {
      const errorInfo = classifyError(error)

      // 记录错误日志
      if (finalConfig.logError) {
        logError(error, errorInfo, context)
      }

      // 显示错误消息
      if (finalConfig.showMessage || finalConfig.showNotification) {
        showErrorMessage(errorInfo, finalConfig)
      }

      // 重新抛出错误，让调用方可以进一步处理
      throw {
        ...error,
        errorInfo,
        context
      }
    }
  }
}

/**
 * 创建带错误处理的API函数
 * @param {Function} apiCall - 原始API函数
 * @param {string} errorMessage - 自定义错误消息
 * @param {Object} config - 错误处理配置
 * @returns {Function} 包装后的API函数
 */
export function createApiWithErrorHandling(apiCall, errorMessage, config = {}) {
  return withErrorHandling(apiCall, {
    ...config,
    customMessage: errorMessage
  })
}

/**
 * 批量创建带错误处理的API函数
 * @param {Object} apiMethods - API方法对象
 * @param {Object} config - 全局错误处理配置
 * @returns {Object} 包装后的API方法对象
 */
export function createApiWithBatchErrorHandling(apiMethods, config = {}) {
  const wrappedMethods = {}
  
  Object.keys(apiMethods).forEach(key => {
    wrappedMethods[key] = withErrorHandling(apiMethods[key], config)
  })
  
  return wrappedMethods
}
