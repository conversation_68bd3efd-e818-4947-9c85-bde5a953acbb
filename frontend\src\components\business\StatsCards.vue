<template>
  <div class="stats-cards">
    <div class="cards-grid">
      <!-- 总数统计卡片 -->
      <div class="stat-card total-card">
        <div class="card-content">
          <div class="stat-icon">
            <el-icon class="icon total-icon"><Tickets /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">
              <LoadingWrapper :loading="loading" inline>
                {{ formatNumber(stats.total) }}
              </LoadingWrapper>
            </div>
            <div class="stat-label">规则明细总数</div>
          </div>
        </div>
        <div class="card-trend" v-if="stats.total_trend">
          <TrendIndicator :trend="stats.total_trend" />
        </div>
      </div>

      <!-- 活跃状态卡片 -->
      <div class="stat-card active-card">
        <div class="card-content">
          <div class="stat-icon">
            <el-icon class="icon active-icon"><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">
              <LoadingWrapper :loading="loading" inline>
                {{ formatNumber(stats.active) }}
              </LoadingWrapper>
            </div>
            <div class="stat-label">活跃规则</div>
            <div class="stat-percentage" v-if="stats.total > 0">
              {{ getPercentage(stats.active, stats.total) }}%
            </div>
          </div>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill active-progress" 
            :style="{ width: getPercentage(stats.active, stats.total) + '%' }"
          ></div>
        </div>
      </div>

      <!-- 非活跃状态卡片 -->
      <div class="stat-card inactive-card">
        <div class="card-content">
          <div class="stat-icon">
            <el-icon class="icon inactive-icon"><Warning /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">
              <LoadingWrapper :loading="loading" inline>
                {{ formatNumber(stats.inactive) }}
              </LoadingWrapper>
            </div>
            <div class="stat-label">非活跃规则</div>
            <div class="stat-percentage" v-if="stats.total > 0">
              {{ getPercentage(stats.inactive, stats.total) }}%
            </div>
          </div>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill inactive-progress" 
            :style="{ width: getPercentage(stats.inactive, stats.total) + '%' }"
          ></div>
        </div>
      </div>

      <!-- 已删除状态卡片 -->
      <div class="stat-card deleted-card">
        <div class="card-content">
          <div class="stat-icon">
            <el-icon class="icon deleted-icon"><Delete /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">
              <LoadingWrapper :loading="loading" inline>
                {{ formatNumber(stats.deleted) }}
              </LoadingWrapper>
            </div>
            <div class="stat-label">已删除规则</div>
            <div class="stat-percentage" v-if="stats.total > 0">
              {{ getPercentage(stats.deleted, stats.total) }}%
            </div>
          </div>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill deleted-progress" 
            :style="{ width: getPercentage(stats.deleted, stats.total) + '%' }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 错误统计详情 -->
    <div class="error-stats" v-if="stats.error_counts && Object.keys(stats.error_counts).length > 0">
      <h4 class="error-title">错误类型分布</h4>
      <div class="error-grid">
        <div 
          v-for="(count, errorType) in stats.error_counts" 
          :key="errorType"
          class="error-item"
        >
          <div class="error-label">{{ formatErrorType(errorType) }}</div>
          <div class="error-count">{{ formatNumber(count) }}</div>
          <div class="error-bar">
            <div 
              class="error-fill" 
              :style="{ 
                width: getPercentage(count, getTotalErrors()) + '%',
                backgroundColor: getErrorColor(errorType)
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Tickets, Check, Warning, Delete } from '@element-plus/icons-vue'
import LoadingWrapper from '@/components/common/LoadingWrapper.vue'
import TrendIndicator from '@/components/common/TrendIndicator.vue'

// Props
const props = defineProps({
  stats: {
    type: Object,
    default: () => ({
      total: 0,
      active: 0,
      inactive: 0,
      deleted: 0,
      error_counts: {}
    })
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 格式化数字
const formatNumber = (num) => {
  if (num === undefined || num === null) return '0'
  return Number(num).toLocaleString('zh-CN')
}

// 计算百分比
const getPercentage = (value, total) => {
  if (!total || total === 0) return 0
  return Math.round((value / total) * 100)
}

// 获取错误总数
const getTotalErrors = () => {
  if (!props.stats.error_counts) return 0
  return Object.values(props.stats.error_counts).reduce((sum, count) => sum + count, 0)
}

// 格式化错误类型
const formatErrorType = (errorType) => {
  const typeMap = {
    'DATA_INTEGRITY': '数据完整性错误',
    'BUSINESS_LOGIC': '业务逻辑错误',
    'FORMAT_ERROR': '格式错误',
    'COMPLIANCE': '合规性错误',
    'REQUIRED_FIELD_MISSING': '必填字段缺失',
    'DATA_TYPE_ERROR': '数据类型错误',
    'VALUE_RANGE_ERROR': '数值范围错误',
    'RELATIONSHIP_ERROR': '关联性错误'
  }
  return typeMap[errorType] || errorType
}

// 获取错误类型颜色
const getErrorColor = (errorType) => {
  const colorMap = {
    'DATA_INTEGRITY': '#f56c6c',
    'BUSINESS_LOGIC': '#e6a23c',
    'FORMAT_ERROR': '#909399',
    'COMPLIANCE': '#f78989',
    'REQUIRED_FIELD_MISSING': '#ff7875',
    'DATA_TYPE_ERROR': '#ffa940',
    'VALUE_RANGE_ERROR': '#40a9ff',
    'RELATIONSHIP_ERROR': '#73d13d'
  }
  return colorMap[errorType] || '#909399'
}
</script>

<style scoped>
.stats-cards {
  margin-bottom: 20px;
}

.cards-grid {
  display: grid;
  gap: 16px;
  margin-bottom: 20px;
}

/* 响应式网格布局 */
@media (min-width: 1200px) {
  .cards-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .cards-grid {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon {
  font-size: 24px;
  color: white;
}

.total-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.active-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.inactive-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.deleted-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-percentage {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.progress-bar {
  height: 4px;
  background: #f5f7fa;
  border-radius: 2px;
  margin-top: 12px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.6s ease;
}

.active-progress {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}

.inactive-progress {
  background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}

.deleted-progress {
  background: linear-gradient(90deg, #ffecd2 0%, #fcb69f 100%);
}

.card-trend {
  position: absolute;
  top: 16px;
  right: 16px;
}

.error-stats {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.error-grid {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.error-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.error-label {
  font-size: 13px;
  color: #606266;
  margin-bottom: 4px;
}

.error-count {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.error-bar {
  height: 3px;
  background: #f0f0f0;
  border-radius: 1.5px;
  overflow: hidden;
}

.error-fill {
  height: 100%;
  border-radius: 1.5px;
  transition: width 0.6s ease;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .stats-cards {
    margin-bottom: 16px;
  }
  
  .cards-grid {
    gap: 12px;
    margin-bottom: 16px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .card-content {
    gap: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
  }
  
  .icon {
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .error-stats {
    padding: 16px;
  }
  
  .error-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
</style>
