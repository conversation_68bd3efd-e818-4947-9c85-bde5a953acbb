<template>
  <div class="upload-mode-section">
    <h3 class="section-title">选择上传模式</h3>
    <el-radio-group
      v-model="selectedMode"
      class="upload-mode-group"
      size="large"
      data-testid="upload-mode-selector"
      @change="handleModeChange"
    >
      <el-radio value="full" class="upload-mode-option">
        <div class="mode-content">
          <div class="mode-title">全量上传</div>
          <div class="mode-description">上传完整的数据集，替换现有数据</div>
        </div>
      </el-radio>
      <el-radio value="incremental" class="upload-mode-option">
        <div class="mode-content">
          <div class="mode-title">增量上传</div>
          <div class="mode-description">只上传变更的数据，支持新增、更新、删除操作</div>
        </div>
      </el-radio>
    </el-radio-group>

    <!-- 模式说明 -->
    <el-alert
      v-if="selectedMode === 'full'"
      title="全量上传说明"
      type="info"
      :closable="false"
      class="mode-alert"
    >
      <ul class="alert-list">
        <li>将完全替换现有的规则数据</li>
        <li>适用于数据结构变更或大量数据更新的场景</li>
        <li>上传前请确保数据的完整性和准确性</li>
      </ul>
    </el-alert>

    <el-alert
      v-else-if="selectedMode === 'incremental'"
      title="增量上传说明"
      type="warning"
      :closable="false"
      class="mode-alert"
    >
      <ul class="alert-list">
        <li>系统将自动对比现有数据，识别新增、更新、删除操作</li>
        <li>基于 rule_id 字段进行数据匹配</li>
        <li>上传前会显示详细的变更预览供您确认</li>
        <li>适用于部分数据更新的场景</li>
      </ul>
    </el-alert>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: 'full'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 内部状态
const selectedMode = ref(props.modelValue)

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedMode.value = newValue
})

// 处理模式变化
const handleModeChange = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped>
.upload-mode-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.section-title {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.upload-mode-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 16px;
}

/* 响应式断点设计 */
@media (min-width: 1200px) {
  .upload-mode-group {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .upload-mode-group {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.upload-mode-option {
  margin-right: 0 !important;
}

.upload-mode-option :deep(.el-radio__label) {
  width: 100%;
  padding-left: 12px;
}

.mode-content {
  padding: 16px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  background-color: var(--el-bg-color);
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-mode-option.is-checked .mode-content {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.mode-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  font-size: 14px;
}

.mode-description {
  font-size: 13px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.mode-alert {
  margin-top: 16px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.alert-list {
  margin: 0;
  padding-left: 20px;
}

.alert-list li {
  margin-bottom: 4px;
  line-height: 1.4;
}

/* 移动端优化 */
@media (max-width: 576px) {
  .upload-mode-section {
    padding: 16px;
    margin-bottom: 20px;
  }
  
  .mode-content {
    padding: 12px;
  }
  
  .section-title {
    font-size: 15px;
  }
}
</style>
