<template>
  <div class="view-toggle">
    <el-radio-group 
      :model-value="modelValue" 
      @update:model-value="handleChange"
      size="small"
      class="toggle-group"
    >
      <el-radio-button 
        v-for="option in options" 
        :key="option"
        :value="option"
        class="toggle-option"
      >
        <el-icon class="toggle-icon">
          <Grid v-if="option === 'card'" />
          <List v-else-if="option === 'table'" />
          <component :is="getIcon(option)" v-else />
        </el-icon>
        <span class="toggle-text">{{ getLabel(option) }}</span>
      </el-radio-button>
    </el-radio-group>
  </div>
</template>

<script setup>
import { Grid, List, View } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    required: true
  },
  options: {
    type: Array,
    default: () => ['card', 'table']
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 处理变化
const handleChange = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
}

// 获取图标
const getIcon = (option) => {
  const iconMap = {
    card: Grid,
    table: List,
    list: List,
    grid: Grid
  }
  return iconMap[option] || View
}

// 获取标签
const getLabel = (option) => {
  const labelMap = {
    card: '卡片',
    table: '表格',
    list: '列表',
    grid: '网格'
  }
  return labelMap[option] || option
}
</script>

<style scoped>
.view-toggle {
  display: inline-block;
}

.toggle-group {
  border-radius: 4px;
  overflow: hidden;
}

.toggle-option {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 12px;
}

.toggle-icon {
  font-size: 14px;
}

.toggle-text {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .toggle-text {
    display: none;
  }
  
  .toggle-option {
    padding: 0 8px;
  }
}
</style>
