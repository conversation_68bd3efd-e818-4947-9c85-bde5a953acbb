/**
 * 规则详情管理Composable
 * 封装规则详情的获取、展示和操作逻辑
 */
import { ref, computed, watch, toRef } from 'vue'
import { useRulesStore } from '@/stores/rules'
import { useRuleDetailsStore } from '@/stores/ruleDetails'
import { ElMessage } from 'element-plus'

/**
 * 规则详情功能
 */
export function useRuleDetail() {
  const rulesStore = useRulesStore()
  const ruleDetailsStore = useRuleDetailsStore() // 新增

  // Store状态 - 安全的响应式访问
  const currentRule = toRef(rulesStore, 'currentRule')
  const ruleSchema = toRef(rulesStore, 'ruleSchema')
  const ruleStatistics = toRef(rulesStore, 'ruleStatistics')
  const detailLoading = toRef(rulesStore, 'detailLoading')

  // 规则明细状态（新增）- 安全的响应式访问
  const currentRuleDetails = toRef(rulesStore, 'currentRuleDetails')
  const detailsCount = toRef(rulesStore, 'detailsCount')
  const ruleDetailsLoading = toRef(rulesStore, 'detailsLoading')
  const hasRuleDetails = toRef(rulesStore, 'hasRuleDetails')
  const ruleDetailsCount = toRef(rulesStore, 'ruleDetailsCount')

  // 本地状态
  const isVisible = ref(false)
  const currentRuleKey = ref('')

  // 计算属性
  const drawerTitle = computed(() => {
    return currentRule.value?.rule_name ? `规则详情 - ${currentRule.value.rule_name}` : '规则详情'
  })

  const hasRuleData = computed(() => {
    return currentRule.value?.rule_key
  })

  const ruleSchemaFormatted = computed(() => {
    const schema = ruleSchema.value || []
    if (!Array.isArray(schema)) return []
    return schema.map(item => ({
      ...item,
      typeDisplay: formatParameterType(item?.type),
      requiredDisplay: item?.required ? '必填' : '可选',
      description: getParameterDescription(item)
    }))
  })

  const ruleStatsFormatted = computed(() => {
    const stats = ruleStatistics.value || {}
    if (!stats || typeof stats !== 'object') return []
    return Object.entries(stats).map(([key, value]) => ({
      key,
      label: formatStatLabel(key),
      value: formatStatValue(key, value)
    }))
  })

  // 获取规则详情
  const fetchRuleDetail = async (ruleKey) => {
    if (!ruleKey) return null

    try {
      currentRuleKey.value = ruleKey
      const result = await rulesStore.fetchRuleDetail(ruleKey)
      return result
    } catch (error) {
      console.error('获取规则详情失败:', error)
      return null
    }
  }

  // 清除当前规则
  const clearCurrentRule = () => {
    rulesStore.clearCurrentRule()
    currentRuleKey.value = ''
  }

  // 显示规则详情
  const showRuleDetail = async (ruleKey) => {
    if (ruleKey) {
      await fetchRuleDetail(ruleKey)
      isVisible.value = true
    }
  }

  // 隐藏规则详情
  const hideRuleDetail = () => {
    isVisible.value = false
    clearCurrentRule()
  }

  // 监听可见性变化
  watch(isVisible, (newVisible) => {
    if (!newVisible) {
      clearCurrentRule()
    }
  })

  // 格式化参数类型
  const formatParameterType = (type) => {
    const typeMap = {
      'string': '文本',
      'number': '数字',
      'boolean': '布尔',
      'date': '日期',
      'datetime': '日期时间',
      'array': '数组',
      'object': '对象'
    }
    return typeMap[type] || type
  }

  // 获取参数描述
  const getParameterDescription = (param) => {
    const parts = []

    if (param.description) {
      parts.push(param.description)
    }

    if (param.example) {
      parts.push(`示例: ${param.example}`)
    }

    if (param.constraints) {
      parts.push(`约束: ${param.constraints}`)
    }

    return parts.join(' | ') || '暂无说明'
  }

  // 格式化统计标签
  const formatStatLabel = (key) => {
    const labelMap = {
      'total_executions': '总执行次数',
      'success_rate': '成功率',
      'avg_execution_time': '平均执行时间',
      'last_execution': '最后执行时间',
      'error_count': '错误次数',
      'last_error': '最后错误时间'
    }
    return labelMap[key] || key
  }

  // 格式化统计值
  const formatStatValue = (key, value) => {
    if (value === null || value === undefined) {
      return '暂无数据'
    }

    switch (key) {
      case 'success_rate':
        return `${(value * 100).toFixed(1)}%`
      case 'avg_execution_time':
        return `${value}ms`
      case 'last_execution':
      case 'last_error':
        return new Date(value).toLocaleString()
      default:
        return String(value)
    }
  }

  // 获取类型标签类型
  const getTypeTagType = (type) => {
    const typeTagMap = {
      'string': '',
      'number': 'success',
      'boolean': 'warning',
      'date': 'info',
      'datetime': 'info',
      'array': 'danger',
      'object': 'danger'
    }
    return typeTagMap[type] || ''
  }

  // ==================== 规则明细相关方法（新增） ====================

  /**
   * 获取规则明细列表
   * @param {string} ruleKey - 规则键
   * @returns {Promise<Array>} 明细列表
   */
  const fetchRuleDetailsList = async (ruleKey) => {
    if (!ruleKey) return []

    try {
      return await rulesStore.fetchRuleDetailsList(ruleKey)
    } catch (error) {
      console.error('获取规则明细列表失败:', error)
      ElMessage.error('获取规则明细列表失败')
      return []
    }
  }

  /**
   * 获取规则明细数量
   * @param {string} ruleKey - 规则键
   * @returns {Promise<number>} 明细数量
   */
  const getRuleDetailsCount = async (ruleKey) => {
    if (!ruleKey) return 0

    try {
      return await rulesStore.getRuleDetailsCount(ruleKey)
    } catch (error) {
      console.error('获取规则明细数量失败:', error)
      return 0
    }
  }

  /**
   * 刷新规则明细
   * @param {string} ruleKey - 规则键
   * @returns {Promise<Array>} 明细列表
   */
  const refreshRuleDetails = async (ruleKey) => {
    if (!ruleKey) return []

    try {
      return await rulesStore.refreshRuleDetails(ruleKey)
    } catch (error) {
      console.error('刷新规则明细失败:', error)
      ElMessage.error('刷新规则明细失败')
      return []
    }
  }

  return {
    // 响应式数据
    currentRule,
    ruleSchema,
    ruleStatistics,
    detailLoading,
    isVisible,
    currentRuleKey,
    drawerTitle,
    hasRuleData,
    ruleSchemaFormatted,
    ruleStatsFormatted,

    // 规则明细状态（新增）
    currentRuleDetails,
    detailsCount,
    ruleDetailsLoading,
    hasRuleDetails,
    ruleDetailsCount,

    // 方法
    fetchRuleDetail,
    clearCurrentRule,
    showRuleDetail,
    hideRuleDetail,
    formatParameterType,
    getParameterDescription,
    formatStatLabel,
    formatStatValue,
    getTypeTagType,

    // 规则明细方法（新增）
    fetchRuleDetailsList,
    getRuleDetailsCount,
    refreshRuleDetails
  }
}

/**
 * 规则详情抽屉管理
 */
export function useRuleDetailDrawer(props, emit) {
  const {
    currentRule,
    ruleSchema,
    ruleStatistics,
    detailLoading,
    drawerTitle,
    ruleSchemaFormatted,
    ruleStatsFormatted,
    fetchRuleDetail,
    clearCurrentRule,
    getTypeTagType
  } = useRuleDetail()

  // 抽屉可见性
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 监听ruleKey变化
  watch(() => props.ruleKey, async (newRuleKey) => {
    if (newRuleKey && visible.value) {
      await fetchRuleDetail(newRuleKey)
    }
  }, { immediate: true })

  // 监听抽屉显示状态
  watch(visible, async (newVisible) => {
    if (newVisible && props.ruleKey) {
      await fetchRuleDetail(props.ruleKey)
    } else if (!newVisible) {
      clearCurrentRule()
    }
  })

  // 关闭抽屉
  const handleClose = () => {
    visible.value = false
  }

  // 下载模板
  const handleDownloadTemplate = () => {
    if (currentRule.value) {
      emit('download-template', currentRule.value)
    }
  }

  // 上传数据
  const handleUploadData = () => {
    if (currentRule.value) {
      emit('upload-data', currentRule.value)
    }
  }

  return {
    // 响应式数据
    visible,
    currentRule,
    ruleSchema,
    ruleStatistics,
    detailLoading,
    drawerTitle,
    ruleSchemaFormatted,
    ruleStatsFormatted,

    // 方法
    handleClose,
    handleDownloadTemplate,
    handleUploadData,
    getTypeTagType
  }
}
