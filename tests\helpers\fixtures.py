"""
通用测试夹具
提供可复用的测试夹具
"""

from datetime import datetime
from typing import Any
from unittest.mock import Mock

import pytest

from models.database import BaseRule, MigrationStatusEnum, RuleDataSet, RuleDetail, RuleDetailStatusEnum, RuleStatusEnum

# ============================================================================
# 数据库模型夹具
# ============================================================================

@pytest.fixture
def base_rule_factory():
    """基础规则工厂"""
    def _create_base_rule(**kwargs) -> BaseRule:
        default_data = {
            "rule_key": "test_rule_001",
            "rule_name": "测试规则001",
            "description": "测试规则描述",
            "module_path": "test.module.path",
            "file_hash": "test_hash_123",
            "status": RuleStatusEnum.READY,
            "created_by": "test_user",
        }
        default_data.update(kwargs)
        return BaseRule(**default_data)

    return _create_base_rule


@pytest.fixture
def rule_dataset_factory():
    """规则数据集工厂"""
    def _create_rule_dataset(**kwargs) -> RuleDataSet:
        default_data = {
            "base_rule_id": 1,
            "version": 1,
            "is_active": True,
            "uploaded_by": "test_user",
            "migration_status": MigrationStatusEnum.PENDING,
        }
        default_data.update(kwargs)
        return RuleDataSet(**default_data)

    return _create_rule_dataset


@pytest.fixture
def rule_detail_factory():
    """规则明细工厂"""
    def _create_rule_detail(**kwargs) -> RuleDetail:
        default_data = {
            "dataset_id": 1,
            "rule_detail_id": "test_detail_001",
            "rule_name": "测试明细规则001",
            "error_level_1": "数据质量",
            "error_level_2": "完整性",
            "error_level_3": "字段缺失",
            "error_reason": "测试错误原因",
            "error_severity": "高",
            "status": RuleDetailStatusEnum.ACTIVE,
        }
        default_data.update(kwargs)
        return RuleDetail(**default_data)

    return _create_rule_detail


# ============================================================================
# API响应夹具
# ============================================================================

@pytest.fixture
def api_response_factory():
    """API响应工厂"""
    def _create_api_response(success: bool = True, code: int = 200, message: str = "操作成功", data: Any = None) -> dict[str, Any]:
        return {
            "code": code,
            "success": success,
            "message": message,
            "data": data,
            "timestamp": datetime.now().timestamp(),
        }

    return _create_api_response


# ============================================================================
# 测试数据夹具
# ============================================================================

@pytest.fixture
def sample_patient_data():
    """示例患者数据"""
    return {
        "bah": "TEST001",
        "patientBasicInfo": {
            "gender": "1",
            "age": 30,
            "birthDate": 946684800000,  # 2000-01-01
        },
        "feeItemList": [
            {
                "itemCode": "TEST001",
                "itemName": "测试项目",
                "amount": 100.0,
                "quantity": 1,
            }
        ],
        "diagnosisList": [
            {
                "diagnosisCode": "A00.001",
                "diagnosisName": "测试诊断",
                "diagnosisType": "主诊断",
            }
        ],
    }


@pytest.fixture
def sample_rule_data():
    """示例规则数据"""
    return [
        {
            "rule_id": "001",
            "rule_name": "年龄限制规则",
            "error_level_1": "适应症限制",
            "error_level_2": "年龄限制",
            "error_level_3": "超出年龄范围",
            "error_reason": "患者年龄超出药品适用范围",
            "error_severity": "高",
        },
        {
            "rule_id": "002", 
            "rule_name": "性别限制规则",
            "error_level_1": "适应症限制",
            "error_level_2": "性别限制",
            "error_level_3": "性别不符",
            "error_reason": "该药品仅限男性使用",
            "error_severity": "中",
        }
    ]


# ============================================================================
# Mock对象夹具
# ============================================================================

@pytest.fixture
def mock_rule_service():
    """模拟规则服务"""
    service = Mock()
    service.validate_patient_data.return_value = {"valid": True, "errors": []}
    service.get_rule_by_key.return_value = {"rule_key": "test_rule", "status": "READY"}
    service.load_rules.return_value = True
    return service


@pytest.fixture
def mock_database_service():
    """模拟数据库服务"""
    service = Mock()
    service.get_session.return_value = Mock()
    service.execute_query.return_value = []
    service.commit.return_value = True
    service.rollback.return_value = True
    return service


# ============================================================================
# 文件和路径夹具
# ============================================================================

@pytest.fixture
def temp_rule_file(tmp_path):
    """临时规则文件"""
    rule_file = tmp_path / "test_rule.py"
    rule_file.write_text("""
def validate_rule(patient_data):
    return {"valid": True, "errors": []}
""")
    return str(rule_file)


@pytest.fixture
def temp_excel_file(tmp_path):
    """临时Excel文件"""
    excel_file = tmp_path / "test_data.xlsx"
    # 这里可以创建一个真实的Excel文件用于测试
    # 暂时创建一个空文件
    excel_file.write_bytes(b"")
    return str(excel_file)


# ============================================================================
# 时间相关夹具
# ============================================================================

@pytest.fixture
def fixed_datetime():
    """固定的日期时间"""
    return datetime(2024, 1, 1, 12, 0, 0)


@pytest.fixture
def time_range():
    """时间范围"""
    return {
        "start": datetime(2024, 1, 1, 0, 0, 0),
        "end": datetime(2024, 1, 31, 23, 59, 59),
    }
