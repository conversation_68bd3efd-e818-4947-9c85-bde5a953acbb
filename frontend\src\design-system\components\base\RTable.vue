<template>
  <div :class="tableWrapperClasses">
    <table :class="tableClasses">
      <!-- 表头 -->
      <thead v-if="showHeader" class="r-table__header">
        <tr>
          <th
            v-for="column in columns"
            :key="column.key"
            :class="getColumnClasses(column)"
            :style="getColumnStyles(column)"
            @click="handleSort(column)"
          >
            <div class="r-table__cell-content">
              <span>{{ column.title }}</span>
              
              <!-- 排序图标 -->
              <span v-if="column.sortable" class="r-table__sort-icon">
                <el-icon v-if="sortConfig.key === column.key">
                  <ArrowUp v-if="sortConfig.order === 'asc'" />
                  <ArrowDown v-else />
                </el-icon>
                <el-icon v-else class="r-table__sort-icon--inactive">
                  <Sort />
                </el-icon>
              </span>
            </div>
          </th>
        </tr>
      </thead>
      
      <!-- 表体 -->
      <tbody class="r-table__body">
        <tr
          v-for="(row, index) in paginatedData"
          :key="getRowKey(row, index)"
          :class="getRowClasses(row, index)"
          @click="handleRowClick(row, index)"
        >
          <td
            v-for="column in columns"
            :key="column.key"
            :class="getCellClasses(column, row, index)"
            :style="getCellStyles(column, row, index)"
          >
            <div class="r-table__cell-content">
              <!-- 自定义渲染 -->
              <slot
                v-if="column.slot"
                :name="column.slot"
                :row="row"
                :column="column"
                :index="index"
                :value="getColumnValue(row, column)"
              />
              
              <!-- 默认渲染 -->
              <span v-else>
                {{ formatColumnValue(row, column) }}
              </span>
            </div>
          </td>
        </tr>
        
        <!-- 空数据 -->
        <tr v-if="paginatedData.length === 0" class="r-table__empty">
          <td :colspan="columns.length" class="r-table__empty-cell">
            <div class="r-table__empty-content">
              <slot name="empty">
                <el-empty :description="emptyText" />
              </slot>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    
    <!-- 分页 -->
    <div v-if="showPagination && totalRows > pageSize" class="r-table__pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="totalRows"
        :page-sizes="pageSizes"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElIcon, ElEmpty, ElPagination } from 'element-plus'
import { ArrowUp, ArrowDown, Sort } from '@element-plus/icons-vue'
import { designTokens } from '../../tokens'

const props = defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  
  // 表格列配置
  columns: {
    type: Array,
    default: () => []
  },
  
  // 表格尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },
  
  // 是否显示表头
  showHeader: {
    type: Boolean,
    default: true
  },
  
  // 是否显示边框
  bordered: {
    type: Boolean,
    default: true
  },
  
  // 是否显示斑马纹
  striped: {
    type: Boolean,
    default: false
  },
  
  // 是否可悬浮
  hoverable: {
    type: Boolean,
    default: true
  },
  
  // 行键
  rowKey: {
    type: [String, Function],
    default: 'id'
  },
  
  // 空数据文本
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: false
  },
  
  // 分页大小
  pageSize: {
    type: Number,
    default: 10
  },
  
  // 分页大小选项
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  
  // 分页布局
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  }
})

const emit = defineEmits(['row-click', 'sort-change', 'page-change', 'size-change'])

// 响应式数据
const currentPage = ref(1)
const sortConfig = ref({ key: null, order: null })

// 计算属性
const tableWrapperClasses = computed(() => [
  'r-table-wrapper',
  `r-table-wrapper--${props.size}`
])

const tableClasses = computed(() => [
  'r-table',
  `r-table--${props.size}`,
  {
    'r-table--bordered': props.bordered,
    'r-table--striped': props.striped,
    'r-table--hoverable': props.hoverable
  }
])

const totalRows = computed(() => props.data.length)

const sortedData = computed(() => {
  if (!sortConfig.value.key) return props.data
  
  const column = props.columns.find(col => col.key === sortConfig.value.key)
  if (!column || !column.sortable) return props.data
  
  return [...props.data].sort((a, b) => {
    const aVal = getColumnValue(a, column)
    const bVal = getColumnValue(b, column)
    
    let result = 0
    if (aVal < bVal) result = -1
    else if (aVal > bVal) result = 1
    
    return sortConfig.value.order === 'desc' ? -result : result
  })
})

const paginatedData = computed(() => {
  if (!props.showPagination) return sortedData.value
  
  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return sortedData.value.slice(start, end)
})

// 方法
const getRowKey = (row, index) => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row, index)
  }
  return row[props.rowKey] || index
}

const getColumnValue = (row, column) => {
  if (column.dataIndex) {
    return row[column.dataIndex]
  }
  return row[column.key]
}

const formatColumnValue = (row, column) => {
  const value = getColumnValue(row, column)
  
  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(value, row, column)
  }
  
  return value
}

const getColumnClasses = (column) => [
  'r-table__header-cell',
  {
    'r-table__header-cell--sortable': column.sortable,
    'r-table__header-cell--sorted': sortConfig.value.key === column.key
  }
]

const getColumnStyles = (column) => {
  const styles = {}
  
  if (column.width) {
    styles.width = typeof column.width === 'number' ? `${column.width}px` : column.width
  }
  
  if (column.align) {
    styles.textAlign = column.align
  }
  
  return styles
}

const getRowClasses = (row, index) => [
  'r-table__row',
  {
    'r-table__row--even': index % 2 === 0,
    'r-table__row--odd': index % 2 === 1
  }
]

const getCellClasses = (column, row, index) => [
  'r-table__cell'
]

const getCellStyles = (column, row, index) => {
  const styles = {}
  
  if (column.align) {
    styles.textAlign = column.align
  }
  
  return styles
}

const handleSort = (column) => {
  if (!column.sortable) return
  
  if (sortConfig.value.key === column.key) {
    // 切换排序方向
    if (sortConfig.value.order === 'asc') {
      sortConfig.value.order = 'desc'
    } else {
      sortConfig.value = { key: null, order: null }
    }
  } else {
    // 新的排序列
    sortConfig.value = { key: column.key, order: 'asc' }
  }
  
  emit('sort-change', sortConfig.value)
}

const handleRowClick = (row, index) => {
  emit('row-click', row, index)
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('page-change', page)
}

const handleSizeChange = (size) => {
  currentPage.value = 1
  emit('size-change', size)
}

// 监听数据变化，重置分页
watch(() => props.data, () => {
  currentPage.value = 1
})
</script>

<style scoped>
.r-table-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: v-bind('designTokens.borderRadius.lg');
  border: 1px solid v-bind('designTokens.colors.border.default');
}

.r-table {
  width: 100%;
  border-collapse: collapse;
  background-color: v-bind('designTokens.colors.background.paper');
  font-size: v-bind('designTokens.typography.fontSize.sm');
}

/* 表头样式 */
.r-table__header {
  background-color: v-bind('designTokens.colors.background.neutral');
}

.r-table__header-cell {
  padding: v-bind('designTokens.spacing.padding.table.md');
  font-weight: v-bind('designTokens.typography.fontWeight.semibold');
  color: v-bind('designTokens.colors.text.primary');
  border-bottom: 1px solid v-bind('designTokens.colors.border.default');
  text-align: left;
}

.r-table__header-cell--sortable {
  cursor: pointer;
  user-select: none;
}

.r-table__header-cell--sortable:hover {
  background-color: v-bind('designTokens.colors.background.default');
}

/* 表体样式 */
.r-table__row {
  transition: v-bind('designTokens.transitions.presets.fast');
}

.r-table--striped .r-table__row--even {
  background-color: v-bind('designTokens.colors.background.neutral');
}

.r-table--hoverable .r-table__row:hover {
  background-color: v-bind('designTokens.colors.semantic.primary.lighter');
}

.r-table__cell {
  padding: v-bind('designTokens.spacing.padding.table.md');
  color: v-bind('designTokens.colors.text.secondary');
  border-bottom: 1px solid v-bind('designTokens.colors.border.light');
}

.r-table__cell-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 排序图标 */
.r-table__sort-icon {
  margin-left: v-bind('designTokens.spacing.spacing[1]');
  color: v-bind('designTokens.colors.semantic.primary.main');
}

.r-table__sort-icon--inactive {
  color: v-bind('designTokens.colors.text.tertiary');
}

/* 空数据 */
.r-table__empty-cell {
  padding: v-bind('designTokens.spacing.spacing[12]');
  text-align: center;
}

/* 分页 */
.r-table__pagination {
  padding: v-bind('designTokens.spacing.spacing[4]');
  border-top: 1px solid v-bind('designTokens.colors.border.light');
  display: flex;
  justify-content: flex-end;
}

/* 尺寸变体 */
.r-table--xs .r-table__header-cell,
.r-table--xs .r-table__cell {
  padding: v-bind('designTokens.spacing.padding.table.xs');
  font-size: v-bind('designTokens.typography.fontSize.xs');
}

.r-table--sm .r-table__header-cell,
.r-table--sm .r-table__cell {
  padding: v-bind('designTokens.spacing.padding.table.sm');
  font-size: v-bind('designTokens.typography.fontSize.sm');
}

.r-table--lg .r-table__header-cell,
.r-table--lg .r-table__cell {
  padding: v-bind('designTokens.spacing.padding.table.lg');
  font-size: v-bind('designTokens.typography.fontSize.base');
}

.r-table--xl .r-table__header-cell,
.r-table--xl .r-table__cell {
  padding: v-bind('designTokens.spacing.padding.table.xl');
  font-size: v-bind('designTokens.typography.fontSize.lg');
}
</style>
