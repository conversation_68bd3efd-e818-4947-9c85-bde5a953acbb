/**
 * 规则明细缓存管理工具
 * 提供统一的缓存管理功能，包括缓存策略、失效机制、性能优化等
 */

// 缓存配置
const CACHE_CONFIG = {
  // 缓存过期时间（毫秒）
  EXPIRE_TIME: 30 * 60 * 1000, // 30分钟
  
  // 最大缓存大小
  MAX_CACHE_SIZE: 100,
  
  // 缓存键前缀
  KEY_PREFIX: {
    DETAILS_LIST: 'details_list',
    DETAIL_ITEM: 'detail_item',
    SEARCH_RESULT: 'search_result',
    STATS_DATA: 'stats_data'
  },
  
  // 本地存储键
  STORAGE_KEYS: {
    CACHE_INDEX: 'rule_details_cache_index',
    CACHE_DATA: 'rule_details_cache_data'
  }
}

/**
 * 缓存管理器类
 */
class RuleDetailsCacheManager {
  constructor() {
    this.memoryCache = new Map()
    this.cacheIndex = this.loadCacheIndex()
  }

  // ==================== 缓存键管理 ====================

  /**
   * 生成缓存键
   * @param {string} type - 缓存类型
   * @param {...any} params - 参数
   * @returns {string} 缓存键
   */
  generateKey(type, ...params) {
    const prefix = CACHE_CONFIG.KEY_PREFIX[type.toUpperCase()] || type
    const keyParts = [prefix, ...params.map(p => 
      typeof p === 'object' ? JSON.stringify(p) : String(p)
    )]
    return keyParts.join(':')
  }

  /**
   * 生成哈希键（用于复杂对象）
   * @param {Object} obj - 对象
   * @returns {string} 哈希键
   */
  generateHashKey(obj) {
    const str = JSON.stringify(obj, Object.keys(obj).sort())
    return btoa(str).replace(/[+/=]/g, '')
  }

  // ==================== 内存缓存管理 ====================

  /**
   * 设置内存缓存
   * @param {string} key - 缓存键
   * @param {any} data - 数据
   * @param {number} expireTime - 过期时间（可选）
   */
  setMemoryCache(key, data, expireTime = CACHE_CONFIG.EXPIRE_TIME) {
    // 检查缓存大小限制
    if (this.memoryCache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {
      this.cleanupExpiredMemoryCache()
      
      // 如果清理后仍然超出限制，删除最旧的缓存
      if (this.memoryCache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {
        const firstKey = this.memoryCache.keys().next().value
        this.memoryCache.delete(firstKey)
      }
    }

    const cacheItem = {
      data,
      timestamp: Date.now(),
      expireTime: Date.now() + expireTime,
      accessCount: 0,
      lastAccess: Date.now()
    }

    this.memoryCache.set(key, cacheItem)
    this.updateCacheIndex(key, cacheItem)
  }

  /**
   * 获取内存缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存数据
   */
  getMemoryCache(key) {
    const cacheItem = this.memoryCache.get(key)
    
    if (!cacheItem) {
      return null
    }

    // 检查是否过期
    if (Date.now() > cacheItem.expireTime) {
      this.memoryCache.delete(key)
      this.removeCacheIndex(key)
      return null
    }

    // 更新访问信息
    cacheItem.accessCount++
    cacheItem.lastAccess = Date.now()
    this.updateCacheIndex(key, cacheItem)

    return cacheItem.data
  }

  /**
   * 删除内存缓存
   * @param {string} key - 缓存键
   */
  deleteMemoryCache(key) {
    this.memoryCache.delete(key)
    this.removeCacheIndex(key)
  }

  /**
   * 清理过期的内存缓存
   */
  cleanupExpiredMemoryCache() {
    const now = Date.now()
    const expiredKeys = []

    for (const [key, cacheItem] of this.memoryCache) {
      if (now > cacheItem.expireTime) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => {
      this.memoryCache.delete(key)
      this.removeCacheIndex(key)
    })

    return expiredKeys.length
  }

  /**
   * 清空内存缓存
   */
  clearMemoryCache() {
    this.memoryCache.clear()
    this.clearCacheIndex()
  }

  // ==================== 本地存储缓存管理 ====================

  /**
   * 设置本地存储缓存
   * @param {string} key - 缓存键
   * @param {any} data - 数据
   * @param {number} expireTime - 过期时间（可选）
   */
  setStorageCache(key, data, expireTime = CACHE_CONFIG.EXPIRE_TIME) {
    try {
      const cacheItem = {
        data,
        timestamp: Date.now(),
        expireTime: Date.now() + expireTime
      }

      localStorage.setItem(
        `${CACHE_CONFIG.STORAGE_KEYS.CACHE_DATA}_${key}`,
        JSON.stringify(cacheItem)
      )

      this.updateCacheIndex(key, cacheItem)
    } catch (error) {
      console.warn('设置本地存储缓存失败:', error)
    }
  }

  /**
   * 获取本地存储缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存数据
   */
  getStorageCache(key) {
    try {
      const cacheData = localStorage.getItem(
        `${CACHE_CONFIG.STORAGE_KEYS.CACHE_DATA}_${key}`
      )

      if (!cacheData) {
        return null
      }

      const cacheItem = JSON.parse(cacheData)

      // 检查是否过期
      if (Date.now() > cacheItem.expireTime) {
        this.deleteStorageCache(key)
        return null
      }

      return cacheItem.data
    } catch (error) {
      console.warn('获取本地存储缓存失败:', error)
      return null
    }
  }

  /**
   * 删除本地存储缓存
   * @param {string} key - 缓存键
   */
  deleteStorageCache(key) {
    try {
      localStorage.removeItem(`${CACHE_CONFIG.STORAGE_KEYS.CACHE_DATA}_${key}`)
      this.removeCacheIndex(key)
    } catch (error) {
      console.warn('删除本地存储缓存失败:', error)
    }
  }

  /**
   * 清理过期的本地存储缓存
   */
  cleanupExpiredStorageCache() {
    try {
      const now = Date.now()
      const expiredKeys = []

      // 遍历缓存索引
      for (const key in this.cacheIndex) {
        const cacheItem = this.cacheIndex[key]
        if (now > cacheItem.expireTime) {
          expiredKeys.push(key)
        }
      }

      // 删除过期缓存
      expiredKeys.forEach(key => {
        this.deleteStorageCache(key)
      })

      return expiredKeys.length
    } catch (error) {
      console.warn('清理过期本地存储缓存失败:', error)
      return 0
    }
  }

  /**
   * 清空本地存储缓存
   */
  clearStorageCache() {
    try {
      // 删除所有缓存数据
      for (const key in this.cacheIndex) {
        localStorage.removeItem(`${CACHE_CONFIG.STORAGE_KEYS.CACHE_DATA}_${key}`)
      }

      this.clearCacheIndex()
    } catch (error) {
      console.warn('清空本地存储缓存失败:', error)
    }
  }

  // ==================== 缓存索引管理 ====================

  /**
   * 加载缓存索引
   * @returns {Object} 缓存索引
   */
  loadCacheIndex() {
    try {
      const indexData = localStorage.getItem(CACHE_CONFIG.STORAGE_KEYS.CACHE_INDEX)
      return indexData ? JSON.parse(indexData) : {}
    } catch (error) {
      console.warn('加载缓存索引失败:', error)
      return {}
    }
  }

  /**
   * 保存缓存索引
   */
  saveCacheIndex() {
    try {
      localStorage.setItem(
        CACHE_CONFIG.STORAGE_KEYS.CACHE_INDEX,
        JSON.stringify(this.cacheIndex)
      )
    } catch (error) {
      console.warn('保存缓存索引失败:', error)
    }
  }

  /**
   * 更新缓存索引
   * @param {string} key - 缓存键
   * @param {Object} cacheItem - 缓存项
   */
  updateCacheIndex(key, cacheItem) {
    this.cacheIndex[key] = {
      timestamp: cacheItem.timestamp,
      expireTime: cacheItem.expireTime,
      accessCount: cacheItem.accessCount || 0,
      lastAccess: cacheItem.lastAccess || Date.now()
    }
    this.saveCacheIndex()
  }

  /**
   * 移除缓存索引
   * @param {string} key - 缓存键
   */
  removeCacheIndex(key) {
    delete this.cacheIndex[key]
    this.saveCacheIndex()
  }

  /**
   * 清空缓存索引
   */
  clearCacheIndex() {
    this.cacheIndex = {}
    this.saveCacheIndex()
  }

  // ==================== 统一缓存接口 ====================

  /**
   * 设置缓存（自动选择存储方式）
   * @param {string} key - 缓存键
   * @param {any} data - 数据
   * @param {Object} options - 选项
   */
  set(key, data, options = {}) {
    const {
      expireTime = CACHE_CONFIG.EXPIRE_TIME,
      useStorage = false
    } = options

    // 设置内存缓存
    this.setMemoryCache(key, data, expireTime)

    // 根据选项决定是否设置本地存储缓存
    if (useStorage) {
      this.setStorageCache(key, data, expireTime)
    }
  }

  /**
   * 获取缓存（优先内存，后本地存储）
   * @param {string} key - 缓存键
   * @returns {any} 缓存数据
   */
  get(key) {
    // 优先从内存缓存获取
    let data = this.getMemoryCache(key)
    if (data !== null) {
      return data
    }

    // 从本地存储获取
    data = this.getStorageCache(key)
    if (data !== null) {
      // 将本地存储的数据加载到内存缓存
      this.setMemoryCache(key, data)
      return data
    }

    return null
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  delete(key) {
    this.deleteMemoryCache(key)
    this.deleteStorageCache(key)
  }

  /**
   * 清理所有过期缓存
   * @returns {Object} 清理结果
   */
  cleanup() {
    const memoryCleanup = this.cleanupExpiredMemoryCache()
    const storageCleanup = this.cleanupExpiredStorageCache()

    return {
      memory: memoryCleanup,
      storage: storageCleanup,
      total: memoryCleanup + storageCleanup
    }
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.clearMemoryCache()
    this.clearStorageCache()
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const memorySize = this.memoryCache.size
    const storageSize = Object.keys(this.cacheIndex).length
    const totalSize = memorySize + storageSize

    return {
      memory: {
        size: memorySize,
        maxSize: CACHE_CONFIG.MAX_CACHE_SIZE,
        usage: memorySize / CACHE_CONFIG.MAX_CACHE_SIZE
      },
      storage: {
        size: storageSize
      },
      total: {
        size: totalSize
      },
      config: CACHE_CONFIG
    }
  }
}

// 创建全局缓存管理器实例
const cacheManager = new RuleDetailsCacheManager()

// 定期清理过期缓存
setInterval(() => {
  cacheManager.cleanup()
}, 5 * 60 * 1000) // 每5分钟清理一次

// 导出缓存管理器和工具函数
export default cacheManager

export {
  RuleDetailsCacheManager,
  CACHE_CONFIG
}

/**
 * 便捷的缓存操作函数
 */
export const cache = {
  // 规则明细列表缓存
  setDetailsList: (ruleKey, params, data) => {
    const key = cacheManager.generateKey('DETAILS_LIST', ruleKey, cacheManager.generateHashKey(params))
    cacheManager.set(key, data, { useStorage: true })
  },

  getDetailsList: (ruleKey, params) => {
    const key = cacheManager.generateKey('DETAILS_LIST', ruleKey, cacheManager.generateHashKey(params))
    return cacheManager.get(key)
  },

  // 单条明细缓存
  setDetail: (detailId, data) => {
    const key = cacheManager.generateKey('DETAIL_ITEM', detailId)
    cacheManager.set(key, data)
  },

  getDetail: (detailId) => {
    const key = cacheManager.generateKey('DETAIL_ITEM', detailId)
    return cacheManager.get(key)
  },

  // 搜索结果缓存
  setSearchResult: (ruleKey, searchParams, data) => {
    const key = cacheManager.generateKey('SEARCH_RESULT', ruleKey, cacheManager.generateHashKey(searchParams))
    cacheManager.set(key, data)
  },

  getSearchResult: (ruleKey, searchParams) => {
    const key = cacheManager.generateKey('SEARCH_RESULT', ruleKey, cacheManager.generateHashKey(searchParams))
    return cacheManager.get(key)
  },

  // 统计数据缓存
  setStats: (ruleKey, data) => {
    const key = cacheManager.generateKey('STATS_DATA', ruleKey)
    cacheManager.set(key, data, { useStorage: true })
  },

  getStats: (ruleKey) => {
    const key = cacheManager.generateKey('STATS_DATA', ruleKey)
    return cacheManager.get(key)
  },

  // 清除指定规则的所有缓存
  clearRuleCache: (ruleKey) => {
    const keysToDelete = []
    
    // 查找所有相关的缓存键
    for (const [key] of cacheManager.memoryCache) {
      if (key.includes(ruleKey)) {
        keysToDelete.push(key)
      }
    }
    
    for (const key in cacheManager.cacheIndex) {
      if (key.includes(ruleKey)) {
        keysToDelete.push(key)
      }
    }
    
    // 删除找到的缓存
    keysToDelete.forEach(key => cacheManager.delete(key))
  }
}
