/**
 * 分页功能的Composable
 * 提供完整的分页逻辑，支持多种分页模式和配置
 */
import { ref, computed, watch } from 'vue'

/**
 * 基础分页功能
 * @param {Object} options - 分页配置选项
 * @returns {Object} 分页相关的响应式数据和方法
 */
export function usePagination(options = {}) {
  const {
    defaultPageSize = 10,
    defaultCurrentPage = 1,
    pageSizes = [10, 20, 50, 100],
    showSizeChanger = true,
    showQuickJumper = true,
    showTotal = true,
    layout = 'total, sizes, prev, pager, next, jumper'
  } = options

  // 分页状态
  const currentPage = ref(defaultCurrentPage)
  const pageSize = ref(defaultPageSize)
  const total = ref(0)

  // 计算属性
  const totalPages = computed(() => {
    return Math.ceil(total.value / pageSize.value)
  })

  const startIndex = computed(() => {
    return (currentPage.value - 1) * pageSize.value
  })

  const endIndex = computed(() => {
    return Math.min(startIndex.value + pageSize.value, total.value)
  })

  const hasData = computed(() => {
    return total.value > 0
  })

  const hasPrevPage = computed(() => {
    return currentPage.value > 1
  })

  const hasNextPage = computed(() => {
    return currentPage.value < totalPages.value
  })

  const paginationInfo = computed(() => {
    if (!hasData.value) {
      return '暂无数据'
    }
    return `显示第 ${startIndex.value + 1} 到第 ${endIndex.value} 条记录，共 ${total.value} 条`
  })

  // 分页数据
  const paginatedData = computed(() => {
    if (!options.data || !Array.isArray(options.data.value)) {
      return []
    }
    const data = options.data.value
    return data.slice(startIndex.value, endIndex.value)
  })

  // 页码变化处理
  const handleCurrentChange = (page) => {
    if (page < 1 || page > totalPages.value) return
    currentPage.value = page
  }

  // 页面大小变化处理
  const handleSizeChange = (size) => {
    pageSize.value = size
    // 重新计算当前页，确保不超出范围
    const maxPage = Math.ceil(total.value / size)
    if (currentPage.value > maxPage) {
      currentPage.value = maxPage || 1
    }
  }

  // 跳转到指定页
  const goToPage = (page) => {
    handleCurrentChange(page)
  }

  // 上一页
  const prevPage = () => {
    if (hasPrevPage.value) {
      handleCurrentChange(currentPage.value - 1)
    }
  }

  // 下一页
  const nextPage = () => {
    if (hasNextPage.value) {
      handleCurrentChange(currentPage.value + 1)
    }
  }

  // 第一页
  const firstPage = () => {
    handleCurrentChange(1)
  }

  // 最后一页
  const lastPage = () => {
    handleCurrentChange(totalPages.value)
  }

  // 重置分页
  const resetPagination = () => {
    currentPage.value = defaultCurrentPage
    pageSize.value = defaultPageSize
    total.value = 0
  }

  // 设置总数
  const setTotal = (newTotal) => {
    total.value = newTotal
    // 如果当前页超出范围，调整到最后一页
    if (currentPage.value > totalPages.value) {
      currentPage.value = totalPages.value || 1
    }
  }

  return {
    // 响应式数据
    currentPage,
    pageSize,
    total,
    totalPages,
    startIndex,
    endIndex,
    hasData,
    hasPrevPage,
    hasNextPage,
    paginationInfo,
    paginatedData,

    // 配置
    pageSizes,
    showSizeChanger,
    showQuickJumper,
    showTotal,
    layout,

    // 方法
    handleCurrentChange,
    handleSizeChange,
    goToPage,
    prevPage,
    nextPage,
    firstPage,
    lastPage,
    resetPagination,
    setTotal
  }
}

/**
 * 服务端分页功能
 * 适用于大数据量的服务端分页场景
 */
export function useServerPagination(fetchFunction, options = {}) {
  const {
    defaultPageSize = 10,
    defaultCurrentPage = 1,
    immediate = true
  } = options

  const pagination = usePagination({ 
    defaultPageSize, 
    defaultCurrentPage,
    ...options 
  })

  const loading = ref(false)
  const error = ref(null)

  // 获取数据
  const fetchData = async (params = {}) => {
    if (!fetchFunction) return

    loading.value = true
    error.value = null

    try {
      const requestParams = {
        page: pagination.currentPage.value,
        pageSize: pagination.pageSize.value,
        ...params
      }

      const response = await fetchFunction(requestParams)
      
      // 假设响应格式为 { data: [], total: number }
      if (response && typeof response === 'object') {
        pagination.setTotal(response.total || 0)
        return response.data || []
      }
      
      return response || []
    } catch (err) {
      error.value = err
      console.error('分页数据获取失败:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // 监听分页参数变化，自动重新获取数据
  watch(
    [pagination.currentPage, pagination.pageSize],
    () => {
      fetchData()
    },
    { immediate }
  )

  // 刷新当前页
  const refresh = () => {
    return fetchData()
  }

  // 重置并刷新
  const resetAndRefresh = () => {
    pagination.resetPagination()
    return fetchData()
  }

  return {
    ...pagination,
    loading,
    error,
    fetchData,
    refresh,
    resetAndRefresh
  }
}

/**
 * 虚拟滚动分页
 * 适用于大量数据的虚拟滚动场景
 */
export function useVirtualPagination(data, options = {}) {
  const {
    itemHeight = 50,
    containerHeight = 400,
    buffer = 5
  } = options

  const scrollTop = ref(0)
  const containerRef = ref(null)

  // 计算可见项数量
  const visibleCount = computed(() => {
    return Math.ceil(containerHeight / itemHeight)
  })

  // 计算开始索引
  const startIndex = computed(() => {
    const index = Math.floor(scrollTop.value / itemHeight)
    return Math.max(0, index - buffer)
  })

  // 计算结束索引
  const endIndex = computed(() => {
    const index = startIndex.value + visibleCount.value + buffer * 2
    return Math.min(data.value?.length || 0, index)
  })

  // 可见数据
  const visibleData = computed(() => {
    if (!data.value || !Array.isArray(data.value)) return []
    return data.value.slice(startIndex.value, endIndex.value)
  })

  // 总高度
  const totalHeight = computed(() => {
    return (data.value?.length || 0) * itemHeight
  })

  // 偏移量
  const offsetY = computed(() => {
    return startIndex.value * itemHeight
  })

  // 滚动处理
  const handleScroll = (event) => {
    scrollTop.value = event.target.scrollTop
  }

  // 滚动到指定项
  const scrollToItem = (index) => {
    if (containerRef.value) {
      const targetScrollTop = index * itemHeight
      containerRef.value.scrollTop = targetScrollTop
    }
  }

  return {
    // 响应式数据
    scrollTop,
    containerRef,
    visibleCount,
    startIndex,
    endIndex,
    visibleData,
    totalHeight,
    offsetY,

    // 方法
    handleScroll,
    scrollToItem
  }
}
