<template>
  <button
    :class="buttonClasses"
    :style="buttonStyles"
    :disabled="disabled || loading"
    :type="nativeType"
    @click="handleClick"
  >
    <!-- 加载图标 -->
    <el-icon v-if="loading" class="r-button__loading">
      <Loading />
    </el-icon>
    
    <!-- 前置图标 -->
    <el-icon v-else-if="icon && iconPosition === 'left'" class="r-button__icon r-button__icon--left">
      <component :is="icon" />
    </el-icon>
    
    <!-- 按钮内容 -->
    <span v-if="$slots.default" class="r-button__content">
      <slot />
    </span>
    
    <!-- 后置图标 -->
    <el-icon v-if="icon && iconPosition === 'right' && !loading" class="r-button__icon r-button__icon--right">
      <component :is="icon" />
    </el-icon>
  </button>
</template>

<script setup>
import { computed } from 'vue'
import { ElIcon } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { designTokens } from '../../tokens'

const props = defineProps({
  // 按钮类型
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'error', 'info', 'text', 'link'].includes(value)
  },
  
  // 按钮尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },
  
  // 图标
  icon: {
    type: [String, Object],
    default: null
  },
  
  // 图标位置
  iconPosition: {
    type: String,
    default: 'left',
    validator: (value) => ['left', 'right'].includes(value)
  },
  
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  },
  
  // 是否圆角
  round: {
    type: Boolean,
    default: false
  },
  
  // 是否圆形
  circle: {
    type: Boolean,
    default: false
  },
  
  // 是否朴素按钮
  plain: {
    type: Boolean,
    default: false
  },
  
  // 是否文本按钮
  text: {
    type: Boolean,
    default: false
  },
  
  // 是否链接按钮
  link: {
    type: Boolean,
    default: false
  },
  
  // 原生type属性
  nativeType: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'submit', 'reset'].includes(value)
  },
  
  // 自定义颜色
  color: {
    type: String,
    default: null
  },
  
  // 是否块级按钮
  block: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

// 计算按钮类名
const buttonClasses = computed(() => {
  const { colors, componentSizes } = designTokens
  
  return [
    'r-button',
    `r-button--${props.variant}`,
    `r-button--${props.size}`,
    {
      'r-button--disabled': props.disabled,
      'r-button--loading': props.loading,
      'r-button--round': props.round,
      'r-button--circle': props.circle,
      'r-button--plain': props.plain,
      'r-button--text': props.text,
      'r-button--link': props.link,
      'r-button--block': props.block,
      'r-button--icon-only': !$slots.default && (props.icon || props.loading)
    }
  ]
})

// 计算按钮样式
const buttonStyles = computed(() => {
  const styles = {}
  
  // 自定义颜色
  if (props.color) {
    styles['--r-button-color'] = props.color
  }
  
  return styles
})

// 点击处理
const handleClick = (event) => {
  if (props.disabled || props.loading) return
  emit('click', event)
}
</script>

<style scoped>
.r-button {
  /* 基础样式 */
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  outline: none;
  cursor: pointer;
  user-select: none;
  vertical-align: middle;
  text-decoration: none;
  white-space: nowrap;
  border: 1px solid transparent;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  
  /* 字体样式 */
  font-family: v-bind('designTokens.typography.fontFamily.primary');
  font-weight: v-bind('designTokens.typography.fontWeight.medium');
  line-height: 1;
  
  /* 默认尺寸 */
  height: v-bind('designTokens.componentSizes.button.md.height');
  padding: v-bind('designTokens.componentSizes.button.md.padding');
  font-size: v-bind('designTokens.componentSizes.button.md.fontSize');
  border-radius: v-bind('designTokens.componentSizes.button.md.borderRadius');
  
  /* 添加现代化效果 */
  overflow: hidden;
}

/* 尺寸变体 */
.r-button--xs {
  height: v-bind('designTokens.componentSizes.button.xs.height');
  padding: v-bind('designTokens.componentSizes.button.xs.padding');
  font-size: v-bind('designTokens.componentSizes.button.xs.fontSize');
  border-radius: v-bind('designTokens.componentSizes.button.xs.borderRadius');
}

.r-button--sm {
  height: v-bind('designTokens.componentSizes.button.sm.height');
  padding: v-bind('designTokens.componentSizes.button.sm.padding');
  font-size: v-bind('designTokens.componentSizes.button.sm.fontSize');
  border-radius: v-bind('designTokens.componentSizes.button.sm.borderRadius');
}

.r-button--lg {
  height: v-bind('designTokens.componentSizes.button.lg.height');
  padding: v-bind('designTokens.componentSizes.button.lg.padding');
  font-size: v-bind('designTokens.componentSizes.button.lg.fontSize');
  border-radius: v-bind('designTokens.componentSizes.button.lg.borderRadius');
}

.r-button--xl {
  height: v-bind('designTokens.componentSizes.button.xl.height');
  padding: v-bind('designTokens.componentSizes.button.xl.padding');
  font-size: v-bind('designTokens.componentSizes.button.xl.fontSize');
  border-radius: v-bind('designTokens.componentSizes.button.xl.borderRadius');
}

/* 颜色变体 - 增强渐变效果 */
.r-button--primary {
  color: white;
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.primary.main'), v-bind('designTokens.colors.semantic.primary.light'));
  border-color: v-bind('designTokens.colors.semantic.primary.main');
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.15);
}

.r-button--primary:hover:not(.r-button--disabled) {
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.primary.dark'), v-bind('designTokens.colors.semantic.primary.main'));
  border-color: v-bind('designTokens.colors.semantic.primary.dark');
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
  transform: translateY(-1px);
}

.r-button--primary:active:not(.r-button--disabled) {
  background: v-bind('designTokens.colors.semantic.primary.dark');
  border-color: v-bind('designTokens.colors.semantic.primary.dark');
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  transform: translateY(0);
}

.r-button--secondary {
  color: v-bind('designTokens.colors.text.primary');
  background: linear-gradient(to bottom, #ffffff, #f9fafb);
  border-color: v-bind('designTokens.colors.border.default');
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.r-button--secondary:hover:not(.r-button--disabled) {
  color: v-bind('designTokens.colors.semantic.primary.main');
  border-color: v-bind('designTokens.colors.semantic.primary.main');
  background: linear-gradient(to bottom, #ffffff, #f0f7ff);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
}

.r-button--secondary:active:not(.r-button--disabled) {
  color: v-bind('designTokens.colors.semantic.primary.dark');
  border-color: v-bind('designTokens.colors.semantic.primary.dark');
  background: #f5f7fa;
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.r-button--success {
  color: white;
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.success.main'), v-bind('designTokens.colors.semantic.success.light'));
  border-color: v-bind('designTokens.colors.semantic.success.main');
  box-shadow: 0 2px 6px rgba(34, 197, 94, 0.15);
}

.r-button--success:hover:not(.r-button--disabled) {
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.success.dark'), v-bind('designTokens.colors.semantic.success.main'));
  border-color: v-bind('designTokens.colors.semantic.success.dark');
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.25);
  transform: translateY(-1px);
}

.r-button--warning {
  color: white;
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.warning.main'), v-bind('designTokens.colors.semantic.warning.light'));
  border-color: v-bind('designTokens.colors.semantic.warning.main');
  box-shadow: 0 2px 6px rgba(249, 115, 22, 0.15);
}

.r-button--warning:hover:not(.r-button--disabled) {
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.warning.dark'), v-bind('designTokens.colors.semantic.warning.main'));
  border-color: v-bind('designTokens.colors.semantic.warning.dark');
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25);
  transform: translateY(-1px);
}

.r-button--error {
  color: white;
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.error.main'), v-bind('designTokens.colors.semantic.error.light'));
  border-color: v-bind('designTokens.colors.semantic.error.main');
  box-shadow: 0 2px 6px rgba(239, 68, 68, 0.15);
}

.r-button--error:hover:not(.r-button--disabled) {
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.error.dark'), v-bind('designTokens.colors.semantic.error.main'));
  border-color: v-bind('designTokens.colors.semantic.error.dark');
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.25);
  transform: translateY(-1px);
}

.r-button--info {
  color: white;
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.info.main'), v-bind('designTokens.colors.semantic.info.light'));
  border-color: v-bind('designTokens.colors.semantic.info.main');
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.15);
}

.r-button--info:hover:not(.r-button--disabled) {
  background: linear-gradient(135deg, v-bind('designTokens.colors.semantic.info.dark'), v-bind('designTokens.colors.semantic.info.main'));
  border-color: v-bind('designTokens.colors.semantic.info.dark');
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
  transform: translateY(-1px);
}

.r-button--text {
  color: v-bind('designTokens.colors.semantic.primary.main');
  background-color: transparent;
  border-color: transparent;
  padding-left: 0;
  padding-right: 0;
  position: relative;
}

.r-button--text::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg, v-bind('designTokens.colors.semantic.primary.main'), v-bind('designTokens.colors.semantic.primary.light'));
  transition: width 0.3s ease;
}

.r-button--text:hover:not(.r-button--disabled)::after {
  width: 100%;
}

.r-button--text:hover:not(.r-button--disabled) {
  color: v-bind('designTokens.colors.semantic.primary.dark');
  background-color: transparent;
}

.r-button--link {
  color: v-bind('designTokens.colors.text.link');
  background-color: transparent;
  border-color: transparent;
  padding-left: 0;
  padding-right: 0;
  text-decoration: none;
}

.r-button--link:hover:not(.r-button--disabled) {
  color: v-bind('designTokens.colors.text.linkHover');
  text-decoration: underline;
}

/* 交互状态 */
.r-button--disabled {
  opacity: v-bind('designTokens.opacity[60]');
  cursor: not-allowed;
}

.r-button--loading {
  position: relative;
  pointer-events: none;
}

/* 增强波纹效果 */
.r-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: width 0.6s ease-out, height 0.6s ease-out, opacity 0.6s ease-out;
  z-index: 1;
}

.r-button:active:not(.r-button--disabled)::before {
  width: 300%;
  height: 300%;
  opacity: 1;
  transition: width 0s, height 0s;
}

/* 图标样式 */
.r-button__icon {
  font-size: 1em;
  line-height: 0;
  z-index: 2;
}

.r-button__icon--left {
  margin-right: 0.5em;
  transition: transform 0.2s ease;
}

.r-button__icon--right {
  margin-left: 0.5em;
  transition: transform 0.2s ease;
}

/* 图标悬停效果 */
.r-button:hover:not(.r-button--disabled) .r-button__icon--left {
  transform: translateX(-2px);
}

.r-button:hover:not(.r-button--disabled) .r-button__icon--right {
  transform: translateX(2px);
}

.r-button__loading {
  animation: r-button-loading-rotate 1s linear infinite;
  margin-right: 0.5em;
  z-index: 2;
}

@keyframes r-button-loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 变体组合 */
.r-button--plain {
  background: transparent;
}

.r-button--plain.r-button--primary {
  color: v-bind('designTokens.colors.semantic.primary.main');
  border-color: v-bind('designTokens.colors.semantic.primary.main');
}

.r-button--plain.r-button--primary:hover:not(.r-button--disabled) {
  color: white;
  background-color: v-bind('designTokens.colors.semantic.primary.main');
}

.r-button--round {
  border-radius: 100px;
}

.r-button--circle {
  border-radius: 50%;
  padding: 0;
  width: v-bind('designTokens.componentSizes.button.md.height');
}

.r-button--circle.r-button--xs {
  width: v-bind('designTokens.componentSizes.button.xs.height');
}

.r-button--circle.r-button--sm {
  width: v-bind('designTokens.componentSizes.button.sm.height');
}

.r-button--circle.r-button--lg {
  width: v-bind('designTokens.componentSizes.button.lg.height');
}

.r-button--circle.r-button--xl {
  width: v-bind('designTokens.componentSizes.button.xl.height');
}

.r-button--block {
  display: flex;
  width: 100%;
}

.r-button--icon-only {
  padding: 0;
  width: v-bind('designTokens.componentSizes.button.md.height');
}

.r-button--icon-only.r-button--xs {
  width: v-bind('designTokens.componentSizes.button.xs.height');
}

.r-button--icon-only.r-button--sm {
  width: v-bind('designTokens.componentSizes.button.sm.height');
}

.r-button--icon-only.r-button--lg {
  width: v-bind('designTokens.componentSizes.button.lg.height');
}

.r-button--icon-only.r-button--xl {
  width: v-bind('designTokens.componentSizes.button.xl.height');
}

/* 响应式调整 */
@media (max-width: 640px) {
  .r-button {
    font-size: 0.875rem;
  }
  
  .r-button__icon--left {
    margin-right: 0.3em;
  }
  
  .r-button__icon--right {
    margin-left: 0.3em;
  }
}
</style>
