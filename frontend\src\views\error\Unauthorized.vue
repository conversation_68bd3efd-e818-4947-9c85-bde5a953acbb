<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-icon">
        <el-icon :size="120" color="#F56C6C">
          <Lock />
        </el-icon>
      </div>
      
      <div class="error-content">
        <h1 class="error-title">访问被拒绝</h1>
        <p class="error-description">
          抱歉，您没有权限访问此页面。请联系管理员获取相应权限。
        </p>
        
        <div class="error-details" v-if="showDetails">
          <el-alert
            title="权限信息"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <p><strong>当前角色:</strong> {{ currentRoles.join(', ') }}</p>
              <p><strong>需要角色:</strong> {{ requiredRoles.join(', ') }}</p>
              <p><strong>目标页面:</strong> {{ targetPath }}</p>
            </template>
          </el-alert>
        </div>
        
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
          
          <el-button @click="toggleDetails" text>
            {{ showDetails ? '隐藏详情' : '显示详情' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '../../stores/app'
import { Lock, House, Back } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()

// 响应式数据
const showDetails = ref(false)

// 计算属性
const currentRoles = computed(() => appStore.userRoles || ['guest'])
const requiredRoles = computed(() => route.query.requiredRoles?.split(',') || ['unknown'])
const targetPath = computed(() => route.query.targetPath || '未知页面')

// 方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.error-container {
  text-align: center;
  max-width: 600px;
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.error-icon {
  margin-bottom: 30px;
}

.error-content {
  margin-bottom: 30px;
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.error-description {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 30px;
}

.error-details {
  margin: 20px 0;
  text-align: left;
}

.error-details p {
  margin: 8px 0;
  font-size: 14px;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-actions .el-button {
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    padding: 30px 20px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
