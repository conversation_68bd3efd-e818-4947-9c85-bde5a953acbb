/**
 * 颜色设计Token
 * 定义系统中使用的所有颜色变量，确保UI一致性
 */

// 基础色彩调色板
export const palette = {
  // 主色调 - 蓝色系
  blue: {
    50: '#eff6ff',
    100: '#dbeafe', 
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // 主色
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554'
  },

  // 成功色 - 绿色系
  green: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // 成功色
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16'
  },

  // 警告色 - 橙色系
  orange: {
    50: '#fff7ed',
    100: '#ffedd5',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#f97316', // 警告色
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12',
    950: '#431407'
  },

  // 错误色 - 红色系
  red: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444', // 错误色
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a'
  },

  // 中性色 - 灰色系
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712'
  }
}

// 语义化颜色
export const semantic = {
  // 主要颜色
  primary: {
    lighter: palette.blue[100],
    light: palette.blue[300],
    main: palette.blue[500],
    dark: palette.blue[700],
    darker: palette.blue[900]
  },

  // 成功颜色
  success: {
    lighter: palette.green[100],
    light: palette.green[300],
    main: palette.green[500],
    dark: palette.green[700],
    darker: palette.green[900]
  },

  // 警告颜色
  warning: {
    lighter: palette.orange[100],
    light: palette.orange[300],
    main: palette.orange[500],
    dark: palette.orange[700],
    darker: palette.orange[900]
  },

  // 错误颜色
  error: {
    lighter: palette.red[100],
    light: palette.red[300],
    main: palette.red[500],
    dark: palette.red[700],
    darker: palette.red[900]
  },

  // 信息颜色
  info: {
    lighter: palette.blue[100],
    light: palette.blue[300],
    main: palette.blue[500],
    dark: palette.blue[700],
    darker: palette.blue[900]
  }
}

// 文本颜色
export const text = {
  primary: palette.gray[900],
  secondary: palette.gray[600],
  tertiary: palette.gray[500],
  disabled: palette.gray[400],
  inverse: '#ffffff',
  link: semantic.primary.main,
  linkHover: semantic.primary.dark
}

// 背景颜色
export const background = {
  default: '#ffffff',
  paper: '#ffffff',
  neutral: palette.gray[50],
  disabled: palette.gray[100],
  overlay: 'rgba(0, 0, 0, 0.5)',
  backdrop: 'rgba(0, 0, 0, 0.3)'
}

// 边框颜色
export const border = {
  default: palette.gray[200],
  light: palette.gray[100],
  medium: palette.gray[300],
  dark: palette.gray[400],
  focus: semantic.primary.main,
  error: semantic.error.main,
  success: semantic.success.main,
  warning: semantic.warning.main
}

// 状态颜色映射
export const status = {
  NEW: {
    color: semantic.info.main,
    background: semantic.info.lighter,
    border: semantic.info.light
  },
  CHANGED: {
    color: semantic.warning.main,
    background: semantic.warning.lighter,
    border: semantic.warning.light
  },
  READY: {
    color: semantic.success.main,
    background: semantic.success.lighter,
    border: semantic.success.light
  },
  DEPRECATED: {
    color: semantic.error.main,
    background: semantic.error.lighter,
    border: semantic.error.light
  }
}

// 阴影颜色
export const shadow = {
  light: 'rgba(0, 0, 0, 0.1)',
  medium: 'rgba(0, 0, 0, 0.15)',
  dark: 'rgba(0, 0, 0, 0.2)',
  colored: {
    primary: `rgba(${hexToRgb(semantic.primary.main)}, 0.3)`,
    success: `rgba(${hexToRgb(semantic.success.main)}, 0.3)`,
    warning: `rgba(${hexToRgb(semantic.warning.main)}, 0.3)`,
    error: `rgba(${hexToRgb(semantic.error.main)}, 0.3)`
  }
}

// 渐变色
export const gradient = {
  primary: `linear-gradient(135deg, ${semantic.primary.light} 0%, ${semantic.primary.main} 100%)`,
  success: `linear-gradient(135deg, ${semantic.success.light} 0%, ${semantic.success.main} 100%)`,
  warning: `linear-gradient(135deg, ${semantic.warning.light} 0%, ${semantic.warning.main} 100%)`,
  error: `linear-gradient(135deg, ${semantic.error.light} 0%, ${semantic.error.main} 100%)`,
  neutral: `linear-gradient(135deg, ${palette.gray[100]} 0%, ${palette.gray[200]} 100%)`
}

// 工具函数
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result 
    ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
    : '0, 0, 0'
}

// 颜色工具函数
export const colorUtils = {
  hexToRgb,
  
  // 获取对比色
  getContrastColor: (color) => {
    // 简化的对比度计算
    const rgb = hexToRgb(color).split(', ').map(Number)
    const brightness = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000
    return brightness > 128 ? text.primary : text.inverse
  },

  // 添加透明度
  addAlpha: (color, alpha) => {
    const rgb = hexToRgb(color)
    return `rgba(${rgb}, ${alpha})`
  },

  // 获取状态颜色
  getStatusColor: (status) => {
    return status[status] || status.NEW
  }
}

// 导出所有颜色token
export const colors = {
  palette,
  semantic,
  text,
  background,
  border,
  status,
  shadow,
  gradient,
  ...colorUtils
}
