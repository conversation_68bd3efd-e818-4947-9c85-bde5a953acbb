/**
 * 降级状态管理 Store
 * 管理降级监控相关的状态和数据
 */

import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import {
  getDegradationStatus,
  getComponentsStatus,
  getDegradationMetrics,
  getDegradationEvents,
  getDegradationHistory,
  getDegradationPerformanceImpact,
  triggerDegradation,
  recoverDegradation,
  setDegradationLevel,
  DEGRADATION_LEVELS,
  isDegraded,
  formatDegradationLevel
} from '../api/degradation'

/**
 * 降级状态 Store
 */
export const useDegradationStore = defineStore('degradation', () => {
  // ==================== 基础状态 ====================
  
  /**
   * 当前降级状态
   */
  const currentStatus = ref({
    current_level: DEGRADATION_LEVELS.NORMAL,
    previous_level: DEGRADATION_LEVELS.NORMAL,
    is_degraded: false,
    last_change_time: 0,
    degradation_duration: 0,
    active_triggers: [],
    is_manual_override: false,
    override_reason: null,
    executed_actions_count: 0,
    enabled: true,
    running: true
  })

  /**
   * 组件状态列表
   */
  const componentsStatus = ref([])

  /**
   * 降级指标数据
   */
  const metrics = ref({
    total_degradations: 0,
    successful_degradations: 0,
    failed_degradations: 0,
    total_recoveries: 0,
    successful_recoveries: 0,
    failed_recoveries: 0,
    total_actions_executed: 0,
    successful_actions: 0,
    failed_actions: 0,
    average_degradation_duration: 0,
    current_uptime: 0,
    last_degradation_time: null,
    degradation_success_rate: 0,
    recovery_success_rate: 0
  })

  /**
   * 事件日志列表
   */
  const events = ref([])

  /**
   * 历史记录列表
   */
  const history = ref([])

  /**
   * 性能影响数据
   */
  const performanceImpact = ref({
    overall_status: {},
    components_impact: {},
    estimated_performance_reduction: 0,
    resource_savings: {},
    timestamp: 0
  })

  // ==================== 加载状态 ====================

  /**
   * 数据加载状态
   */
  const loading = ref({
    status: false,
    components: false,
    metrics: false,
    events: false,
    history: false,
    performance: false
  })

  /**
   * 操作执行状态
   */
  const operating = ref({
    trigger: false,
    recover: false,
    setLevel: false
  })

  // ==================== 实时更新配置 ====================

  /**
   * 自动刷新间隔（毫秒）
   */
  const refreshInterval = ref(30000) // 30秒

  /**
   * 自动刷新定时器
   */
  const refreshTimer = ref(null)

  /**
   * 是否启用自动刷新
   */
  const autoRefreshEnabled = ref(true)

  // ==================== 计算属性 ====================

  /**
   * 是否处于降级状态
   */
  const isSystemDegraded = computed(() => isDegraded(currentStatus.value.current_level))

  /**
   * 当前降级级别显示名称
   */
  const currentLevelName = computed(() => formatDegradationLevel(currentStatus.value.current_level))

  /**
   * 降级组件数量
   */
  const degradedComponentsCount = computed(() => 
    componentsStatus.value.filter(component => component.is_degraded).length
  )

  /**
   * 总组件数量
   */
  const totalComponentsCount = computed(() => componentsStatus.value.length)

  /**
   * 降级成功率
   */
  const degradationSuccessRate = computed(() => 
    metrics.value.total_degradations > 0 
      ? (metrics.value.successful_degradations / metrics.value.total_degradations * 100).toFixed(1)
      : '0.0'
  )

  /**
   * 恢复成功率
   */
  const recoverySuccessRate = computed(() => 
    metrics.value.total_recoveries > 0 
      ? (metrics.value.successful_recoveries / metrics.value.total_recoveries * 100).toFixed(1)
      : '0.0'
  )

  /**
   * 是否有任何数据正在加载
   */
  const isAnyLoading = computed(() => 
    Object.values(loading.value).some(status => status)
  )

  /**
   * 是否有任何操作正在执行
   */
  const isAnyOperating = computed(() => 
    Object.values(operating.value).some(status => status)
  )

  // ==================== 数据获取方法 ====================

  /**
   * 获取降级状态
   */
  const fetchStatus = async () => {
    loading.value.status = true
    try {
      const response = await getDegradationStatus()
      if (response.success) {
        currentStatus.value = response.data
      }
    } catch (error) {
      console.error('Failed to fetch degradation status:', error)
    } finally {
      loading.value.status = false
    }
  }

  /**
   * 获取组件状态
   */
  const fetchComponents = async () => {
    loading.value.components = true
    try {
      const response = await getComponentsStatus()
      if (response.success) {
        componentsStatus.value = response.data
      }
    } catch (error) {
      console.error('Failed to fetch components status:', error)
    } finally {
      loading.value.components = false
    }
  }

  /**
   * 获取指标数据
   */
  const fetchMetrics = async () => {
    loading.value.metrics = true
    try {
      const response = await getDegradationMetrics()
      if (response.success) {
        metrics.value = response.data
      }
    } catch (error) {
      console.error('Failed to fetch degradation metrics:', error)
    } finally {
      loading.value.metrics = false
    }
  }

  /**
   * 获取事件日志
   */
  const fetchEvents = async (params = {}) => {
    loading.value.events = true
    try {
      const response = await getDegradationEvents(params)
      if (response.success) {
        events.value = response.data
      }
    } catch (error) {
      console.error('Failed to fetch degradation events:', error)
    } finally {
      loading.value.events = false
    }
  }

  /**
   * 获取历史记录
   */
  const fetchHistory = async (params = {}) => {
    loading.value.history = true
    try {
      const response = await getDegradationHistory(params)
      if (response.success) {
        history.value = response.data
      }
    } catch (error) {
      console.error('Failed to fetch degradation history:', error)
    } finally {
      loading.value.history = false
    }
  }

  /**
   * 获取性能影响数据
   */
  const fetchPerformanceImpact = async () => {
    loading.value.performance = true
    try {
      const response = await getDegradationPerformanceImpact()
      if (response.success) {
        performanceImpact.value = response.data
      }
    } catch (error) {
      console.error('Failed to fetch performance impact:', error)
    } finally {
      loading.value.performance = false
    }
  }

  /**
   * 获取所有数据
   */
  const fetchAllData = async () => {
    await Promise.all([
      fetchStatus(),
      fetchComponents(),
      fetchMetrics(),
      fetchPerformanceImpact()
    ])
  }

  // ==================== 操作方法 ====================

  /**
   * 手动触发降级
   */
  const triggerDegradationAction = async (level, reason, force = false) => {
    operating.value.trigger = true
    try {
      const response = await triggerDegradation({
        level,
        reason,
        force
      })
      
      if (response.success) {
        ElMessage.success('降级操作执行成功')
        ElNotification({
          title: '降级通知',
          message: `系统已降级到${formatDegradationLevel(level)}`,
          type: 'warning'
        })
        
        // 刷新状态数据
        await fetchAllData()
        return true
      } else {
        ElMessage.error(response.message || '降级操作失败')
        return false
      }
    } catch (error) {
      console.error('Failed to trigger degradation:', error)
      ElMessage.error('降级操作执行异常')
      return false
    } finally {
      operating.value.trigger = false
    }
  }

  /**
   * 手动恢复正常状态
   */
  const recoverDegradationAction = async (reason = '手动恢复') => {
    operating.value.recover = true
    try {
      const response = await recoverDegradation(reason)
      
      if (response.success) {
        ElMessage.success('恢复操作执行成功')
        ElNotification({
          title: '恢复通知',
          message: '系统已恢复到正常状态',
          type: 'success'
        })
        
        // 刷新状态数据
        await fetchAllData()
        return true
      } else {
        ElMessage.error(response.message || '恢复操作失败')
        return false
      }
    } catch (error) {
      console.error('Failed to recover degradation:', error)
      ElMessage.error('恢复操作执行异常')
      return false
    } finally {
      operating.value.recover = false
    }
  }

  /**
   * 设置指定降级级别
   */
  const setDegradationLevelAction = async (level, reason, duration = null) => {
    operating.value.setLevel = true
    try {
      const data = { level, reason }
      if (duration) {
        data.duration = duration
      }
      
      const response = await setDegradationLevel(data)
      
      if (response.success) {
        ElMessage.success('级别设置成功')
        ElNotification({
          title: '级别设置通知',
          message: `系统级别已设置为${formatDegradationLevel(level)}`,
          type: 'info'
        })
        
        // 刷新状态数据
        await fetchAllData()
        return true
      } else {
        ElMessage.error(response.message || '级别设置失败')
        return false
      }
    } catch (error) {
      console.error('Failed to set degradation level:', error)
      ElMessage.error('级别设置执行异常')
      return false
    } finally {
      operating.value.setLevel = false
    }
  }

  // ==================== 自动刷新管理 ====================

  /**
   * 启动自动刷新
   */
  const startAutoRefresh = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
    }
    
    if (autoRefreshEnabled.value) {
      refreshTimer.value = setInterval(() => {
        fetchAllData()
      }, refreshInterval.value)
    }
  }

  /**
   * 停止自动刷新
   */
  const stopAutoRefresh = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
  }

  /**
   * 设置自动刷新间隔
   */
  const setRefreshInterval = (interval) => {
    refreshInterval.value = interval
    if (autoRefreshEnabled.value) {
      startAutoRefresh()
    }
  }

  /**
   * 切换自动刷新状态
   */
  const toggleAutoRefresh = () => {
    autoRefreshEnabled.value = !autoRefreshEnabled.value
    if (autoRefreshEnabled.value) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }

  // ==================== 监听器 ====================

  // 监听降级状态变化
  watch(() => currentStatus.value.current_level, (newLevel, oldLevel) => {
    if (newLevel !== oldLevel && oldLevel) {
      console.log(`Degradation level changed: ${oldLevel} -> ${newLevel}`)
    }
  })

  // ==================== 返回公共接口 ====================

  return {
    // 状态数据
    currentStatus,
    componentsStatus,
    metrics,
    events,
    history,
    performanceImpact,
    
    // 加载状态
    loading,
    operating,
    
    // 自动刷新配置
    refreshInterval,
    autoRefreshEnabled,
    
    // 计算属性
    isSystemDegraded,
    currentLevelName,
    degradedComponentsCount,
    totalComponentsCount,
    degradationSuccessRate,
    recoverySuccessRate,
    isAnyLoading,
    isAnyOperating,
    
    // 数据获取方法
    fetchStatus,
    fetchComponents,
    fetchMetrics,
    fetchEvents,
    fetchHistory,
    fetchPerformanceImpact,
    fetchAllData,
    
    // 操作方法
    triggerDegradationAction,
    recoverDegradationAction,
    setDegradationLevelAction,
    
    // 自动刷新管理
    startAutoRefresh,
    stopAutoRefresh,
    setRefreshInterval,
    toggleAutoRefresh
  }
})
