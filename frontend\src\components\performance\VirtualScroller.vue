<!--
  虚拟滚动组件
  用于优化大数据量列表的渲染性能
-->

<template>
  <div 
    ref="containerRef"
    class="virtual-scroller"
    :style="containerStyle"
    @scroll="handleScroll"
  >
    <!-- 占位容器，用于撑开滚动条 -->
    <div 
      class="virtual-scroller-spacer"
      :style="{ height: totalHeight + 'px' }"
    >
      <!-- 可视区域内的实际内容 -->
      <div 
        class="virtual-scroller-content"
        :style="contentStyle"
      >
        <div
          v-for="(item, index) in visibleItems"
          :key="getItemKey(item, startIndex + index)"
          class="virtual-scroller-item"
          :style="getItemStyle(startIndex + index)"
        >
          <slot 
            :item="item" 
            :index="startIndex + index"
            :isVisible="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { debounce } from 'lodash-es'

/**
 * 组件属性定义
 */
const props = defineProps({
  // 数据列表
  items: {
    type: Array,
    required: true,
    default: () => []
  },
  // 每项的高度（固定高度模式）
  itemHeight: {
    type: Number,
    default: 50
  },
  // 是否启用动态高度
  dynamicHeight: {
    type: Boolean,
    default: false
  },
  // 容器高度
  height: {
    type: [Number, String],
    default: 400
  },
  // 缓冲区大小（可视区域外渲染的项目数）
  buffer: {
    type: Number,
    default: 5
  },
  // 获取项目唯一键的函数
  keyField: {
    type: [String, Function],
    default: 'id'
  },
  // 预估项目高度（动态高度模式）
  estimatedItemHeight: {
    type: Number,
    default: 50
  }
})

/**
 * 事件定义
 */
const emit = defineEmits([
  'scroll',
  'visible-change',
  'item-resize'
])

/**
 * 响应式数据
 */
const containerRef = ref(null)
const scrollTop = ref(0)
const containerHeight = ref(0)
const itemHeights = ref(new Map()) // 存储每项的实际高度（动态高度模式）
const isScrolling = ref(false)

/**
 * 计算属性
 */

// 容器样式
const containerStyle = computed(() => ({
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  overflow: 'auto',
  position: 'relative'
}))

// 总高度
const totalHeight = computed(() => {
  if (!props.dynamicHeight) {
    return props.items.length * props.itemHeight
  }
  
  let height = 0
  for (let i = 0; i < props.items.length; i++) {
    height += itemHeights.value.get(i) || props.estimatedItemHeight
  }
  return height
})

// 可视区域开始索引
const startIndex = computed(() => {
  if (!props.dynamicHeight) {
    return Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - props.buffer)
  }
  
  let height = 0
  let index = 0
  
  while (height < scrollTop.value && index < props.items.length) {
    height += itemHeights.value.get(index) || props.estimatedItemHeight
    index++
  }
  
  return Math.max(0, index - props.buffer - 1)
})

// 可视区域结束索引
const endIndex = computed(() => {
  if (!props.dynamicHeight) {
    const visibleCount = Math.ceil(containerHeight.value / props.itemHeight)
    return Math.min(props.items.length - 1, startIndex.value + visibleCount + props.buffer * 2)
  }
  
  let height = getOffsetTop(startIndex.value)
  let index = startIndex.value
  
  while (height < scrollTop.value + containerHeight.value + props.buffer * props.estimatedItemHeight && index < props.items.length) {
    height += itemHeights.value.get(index) || props.estimatedItemHeight
    index++
  }
  
  return Math.min(props.items.length - 1, index)
})

// 可视区域内的项目
const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value + 1)
})

// 内容区域样式
const contentStyle = computed(() => ({
  transform: `translateY(${getOffsetTop(startIndex.value)}px)`,
  position: 'relative'
}))

/**
 * 方法定义
 */

/**
 * 获取项目的唯一键
 */
function getItemKey(item, index) {
  if (typeof props.keyField === 'function') {
    return props.keyField(item, index)
  }
  return item[props.keyField] || index
}

/**
 * 获取项目样式
 */
function getItemStyle(index) {
  if (!props.dynamicHeight) {
    return {
      height: `${props.itemHeight}px`,
      overflow: 'hidden'
    }
  }
  
  return {
    minHeight: `${props.estimatedItemHeight}px`
  }
}

/**
 * 获取指定索引项目的顶部偏移量
 */
function getOffsetTop(index) {
  if (!props.dynamicHeight) {
    return index * props.itemHeight
  }
  
  let offset = 0
  for (let i = 0; i < index; i++) {
    offset += itemHeights.value.get(i) || props.estimatedItemHeight
  }
  return offset
}

/**
 * 处理滚动事件
 */
const handleScroll = debounce((event) => {
  scrollTop.value = event.target.scrollTop
  isScrolling.value = true
  
  emit('scroll', {
    scrollTop: scrollTop.value,
    startIndex: startIndex.value,
    endIndex: endIndex.value
  })
  
  // 滚动结束后重置状态
  setTimeout(() => {
    isScrolling.value = false
  }, 150)
}, 16) // 60fps

/**
 * 更新容器高度
 */
function updateContainerHeight() {
  if (containerRef.value) {
    containerHeight.value = containerRef.value.clientHeight
  }
}

/**
 * 测量项目高度（动态高度模式）
 */
function measureItemHeights() {
  if (!props.dynamicHeight || !containerRef.value) return
  
  const items = containerRef.value.querySelectorAll('.virtual-scroller-item')
  items.forEach((item, index) => {
    const actualIndex = startIndex.value + index
    const height = item.offsetHeight
    
    if (height > 0 && itemHeights.value.get(actualIndex) !== height) {
      itemHeights.value.set(actualIndex, height)
      emit('item-resize', { index: actualIndex, height })
    }
  })
}

/**
 * 滚动到指定项目
 */
function scrollToItem(index, alignment = 'auto') {
  if (!containerRef.value || index < 0 || index >= props.items.length) return
  
  const offsetTop = getOffsetTop(index)
  const itemHeight = itemHeights.value.get(index) || props.itemHeight
  
  let scrollTo = offsetTop
  
  switch (alignment) {
    case 'start':
      scrollTo = offsetTop
      break
    case 'center':
      scrollTo = offsetTop - (containerHeight.value - itemHeight) / 2
      break
    case 'end':
      scrollTo = offsetTop - containerHeight.value + itemHeight
      break
    case 'auto':
    default:
      if (offsetTop < scrollTop.value) {
        scrollTo = offsetTop
      } else if (offsetTop + itemHeight > scrollTop.value + containerHeight.value) {
        scrollTo = offsetTop - containerHeight.value + itemHeight
      } else {
        return // 已在可视区域内
      }
      break
  }
  
  containerRef.value.scrollTop = Math.max(0, scrollTo)
}

/**
 * 获取可视区域信息
 */
function getVisibleRange() {
  return {
    start: startIndex.value,
    end: endIndex.value,
    total: props.items.length
  }
}

/**
 * 生命周期钩子
 */
onMounted(() => {
  updateContainerHeight()
  
  // 监听窗口大小变化
  window.addEventListener('resize', updateContainerHeight)
  
  // 动态高度模式下，监听DOM变化
  if (props.dynamicHeight) {
    nextTick(() => {
      measureItemHeights()
    })
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerHeight)
})

/**
 * 监听器
 */
watch(
  () => [startIndex.value, endIndex.value],
  ([newStart, newEnd], [oldStart, oldEnd]) => {
    emit('visible-change', {
      startIndex: newStart,
      endIndex: newEnd,
      visibleItems: visibleItems.value
    })
    
    // 动态高度模式下，重新测量高度
    if (props.dynamicHeight) {
      nextTick(() => {
        measureItemHeights()
      })
    }
  }
)

watch(
  () => props.items.length,
  () => {
    // 数据变化时重置高度缓存
    if (props.dynamicHeight) {
      itemHeights.value.clear()
      nextTick(() => {
        measureItemHeights()
      })
    }
  }
)

/**
 * 暴露给父组件的方法
 */
defineExpose({
  scrollToItem,
  getVisibleRange,
  measureItemHeights,
  containerRef
})
</script>

<style scoped>
.virtual-scroller {
  position: relative;
  overflow: auto;
}

.virtual-scroller-spacer {
  position: relative;
}

.virtual-scroller-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-scroller-item {
  position: relative;
}

/* 滚动条样式优化 */
.virtual-scroller::-webkit-scrollbar {
  width: 8px;
}

.virtual-scroller::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-scroller::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.virtual-scroller::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
