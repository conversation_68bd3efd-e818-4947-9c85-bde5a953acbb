<template>
  <div class="breadcrumb-container" v-if="breadcrumbs.length > 0">
    <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb">
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbs"
        :key="index"
        :to="item.to"
        :class="{ 'is-current': index === breadcrumbs.length - 1 }"
      >
        <el-icon v-if="item.icon" class="breadcrumb-icon">
          <component :is="item.icon" />
        </el-icon>
        <span>{{ item.text }}</span>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'

const route = useRoute()

// 计算面包屑导航
const breadcrumbs = computed(() => {
  const breadcrumb = route.meta.breadcrumb || []
  
  // 如果是动态路由，需要替换参数
  return breadcrumb.map(item => {
    if (item.to && typeof item.to === 'string') {
      // 替换路由参数
      let path = item.to
      Object.keys(route.params).forEach(key => {
        path = path.replace(`:${key}`, route.params[key])
      })
      
      return {
        ...item,
        to: path
      }
    }
    return item
  })
})

// 定义组件属性
defineProps({
  // 可以通过props自定义样式
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  showIcon: {
    type: Boolean,
    default: true
  }
})
</script>

<style scoped>
.breadcrumb-container {
  padding: 12px 0;
  background: #fff;
  border-bottom: 1px solid #EBEEF5;
}

.breadcrumb {
  font-size: 14px;
}

.breadcrumb-icon {
  margin-right: 4px;
  font-size: 12px;
}

.is-current {
  color: #409EFF;
  font-weight: 500;
}

/* 不同尺寸的样式 */
.breadcrumb-container.size-small {
  padding: 8px 0;
}

.breadcrumb-container.size-small .breadcrumb {
  font-size: 12px;
}

.breadcrumb-container.size-large {
  padding: 16px 0;
}

.breadcrumb-container.size-large .breadcrumb {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .breadcrumb-container {
    padding: 8px 0;
  }
  
  .breadcrumb {
    font-size: 12px;
  }
  
  .breadcrumb-icon {
    display: none;
  }
}
</style>
