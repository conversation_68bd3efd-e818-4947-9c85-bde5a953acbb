#!/usr/bin/env python3
"""
测试数据迁移功能
验证 rule_details 表和迁移服务的完整功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from config.settings import settings
from models.database import BaseRule, MigrationStatusEnum, RuleDataSet, RuleDetail, RuleStatusEnum
from services.rule_data_migration import RuleDataMigration


def main():
    """主函数"""
    print("=" * 60)
    print("数据迁移功能测试")
    print("=" * 60)

    # 获取数据库连接
    database_url = settings.get_database_url()
    if not database_url:
        print("❌ 无法获取数据库连接URL")
        return 1

    try:
        engine = create_engine(database_url)
        SessionFactory = sessionmaker(bind=engine)  # noqa: N806

        with SessionFactory() as session:
            # 1. 验证表结构
            print("\n1. 验证表结构:")
            print("-" * 40)

            # 检查 rule_details 表
            result = session.execute(
                text("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'rule_details'")
            )
            if result.scalar() > 0:
                print("  ✅ rule_details 表存在")
            else:
                print("  ❌ rule_details 表不存在")
                return 1

            # 检查 rule_data_sets 表的迁移字段
            result = session.execute(
                text(
                    "SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'rule_data_sets' AND column_name = 'migration_status'"  # noqa: E501
                )
            )
            if result.scalar() > 0:
                print("  ✅ rule_data_sets.migration_status 字段存在")
            else:
                print("  ❌ rule_data_sets.migration_status 字段不存在")
                return 1

            # 2. 清理并创建测试数据
            print("\n2. 清理并创建测试数据:")
            print("-" * 40)

            # 清理可能存在的测试数据
            existing_rule = session.query(BaseRule).filter(BaseRule.rule_key == "test_migration_rule").first()
            if existing_rule:
                # 先删除相关的数据集
                existing_datasets = (
                    session.query(RuleDataSet).filter(RuleDataSet.base_rule_id == existing_rule.id).all()
                )
                for dataset in existing_datasets:
                    session.delete(dataset)
                session.delete(existing_rule)
                session.commit()
                print("  ✅ 清理已存在的测试数据")

            # 创建测试规则
            test_rule = BaseRule(
                rule_key="test_migration_rule",
                rule_name="测试迁移规则",
                description="用于测试数据迁移的规则",
                module_path="test.migration.rule",
                file_hash="test_hash_migration",
                status=RuleStatusEnum.READY,
            )
            session.add(test_rule)
            session.commit()
            session.refresh(test_rule)
            print(f"  ✅ 创建测试规则: {test_rule.rule_key}")

            # 创建测试数据集（包含 JSON 数据）
            test_json_data = [
                {
                    "rule_id": "test_detail_001",
                    "rule_name": "测试明细规则1",
                    "error_level_1": "数据质量",
                    "error_level_2": "完整性检查",
                    "error_reason": "测试错误原因1",
                    "involved_amount": "1000.50",
                    "usage_quantity": "10",
                    "default_selected": "true",
                },
                {
                    "rule_id": "test_detail_002",
                    "rule_name": "测试明细规则2",
                    "error_level_1": "业务逻辑",
                    "error_level_2": "规则验证",
                    "violation_quantity": "5",
                    "effective_start_time": "2024-01-01 00:00:00",
                },
            ]

            test_dataset = RuleDataSet(
                base_rule_id=test_rule.id,
                data_set=test_json_data,
                version=1,
                is_active=True,
                uploaded_by="test_user",
                migration_status=MigrationStatusEnum.PENDING,
            )
            session.add(test_dataset)
            session.commit()
            session.refresh(test_dataset)
            print(f"  ✅ 创建测试数据集: {test_dataset.id} (包含 {len(test_json_data)} 条明细)")

            # 3. 测试数据迁移
            print("\n3. 测试数据迁移:")
            print("-" * 40)

            # 检查待迁移的数据集
            pending_datasets = (
                session.query(RuleDataSet).filter(RuleDataSet.migration_status == MigrationStatusEnum.PENDING).all()
            )
            print(f"  待迁移数据集数量: {len(pending_datasets)}")
            for dataset in pending_datasets:
                print(
                    f"    - 数据集 {dataset.id}: 状态={dataset.migration_status.value}, 数据量={len(dataset.data_set) if dataset.data_set else 0}"  # noqa: E501
                )

            migration_service = RuleDataMigration(SessionFactory)

            # 执行迁移
            result = migration_service.migrate_all_datasets()
            print("  ✅ 迁移执行完成")
            print(f"     - 总数据集: {result['total_datasets']}")
            print(f"     - 成功数据集: {result['successful_datasets']}")
            print(f"     - 失败数据集: {result['failed_datasets']}")
            print(f"     - 总明细数: {result['total_details']}")
            print(f"     - 错误信息: {result['errors']}")
            print(f"     - 警告信息: {result['warnings']}")

        # 4. 验证迁移结果（使用新的会话）
        with SessionFactory() as session:
            print("\n4. 验证迁移结果:")
            print("-" * 40)

            # 重新查询数据集状态
            test_dataset = session.query(RuleDataSet).filter(RuleDataSet.id == test_dataset.id).first()
            print(f"  数据集迁移状态: {test_dataset.migration_status.value}")

            # 检查明细数据
            details = session.query(RuleDetail).filter(RuleDetail.dataset_id == test_dataset.id).all()
            print(f"  ✅ 迁移明细数量: {len(details)}")

            for detail in details:
                print(f"     - {detail.rule_detail_id}: {detail.rule_name}")
                print(f"       错误级别: {detail.error_level_1} -> {detail.error_level_2}")
                if detail.involved_amount:
                    print(f"       涉及金额: {detail.involved_amount}")
                if detail.usage_quantity:
                    print(f"       使用数量: {detail.usage_quantity}")

            # 5. 测试查询性能
            print("\n5. 测试查询性能:")
            print("-" * 40)

            import time

            # 测试关系查询
            start_time = time.time()
            result = (
                session.query(RuleDetail)
                .filter(RuleDetail.dataset_id == test_dataset.id, RuleDetail.error_level_1 == "数据质量")
                .all()
            )
            query_time = time.time() - start_time
            print(f"  ✅ 关系查询耗时: {query_time:.4f}秒 (查询到 {len(result)} 条记录)")

            # 6. 清理测试数据
            print("\n6. 清理测试数据:")
            print("-" * 40)

            # 删除测试数据（级联删除会自动删除明细）
            test_rule = session.query(BaseRule).filter(BaseRule.rule_key == "test_migration_rule").first()
            if test_rule:
                session.delete(test_dataset)
                session.delete(test_rule)
                session.commit()
                print("  ✅ 测试数据清理完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return 1

    print("\n" + "=" * 60)
    print("数据迁移功能测试完成 ✅")
    print("=" * 60)
    return 0


if __name__ == "__main__":
    sys.exit(main())
