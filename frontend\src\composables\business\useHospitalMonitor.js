/**
 * 医院节点监控Composable
 * 封装医院节点状态监控、版本管理和健康检查功能
 */
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'

/**
 * 医院节点监控功能
 */
export function useHospitalMonitor() {
  // 节点状态
  const nodes = ref([])
  const loading = ref(false)
  const lastUpdateTime = ref(null)

  // 监控配置
  const monitorConfig = reactive({
    autoRefresh: true,
    refreshInterval: 30000, // 30秒
    alertThreshold: {
      responseTime: 5000, // 5秒
      errorRate: 0.1, // 10%
      memoryUsage: 0.8 // 80%
    }
  })

  // 计算属性
  const totalNodes = computed(() => nodes.value.length)
  
  const onlineNodes = computed(() => 
    nodes.value.filter(node => node.status === 'online').length
  )
  
  const offlineNodes = computed(() => 
    nodes.value.filter(node => node.status === 'offline').length
  )
  
  const warningNodes = computed(() => 
    nodes.value.filter(node => node.status === 'warning').length
  )

  const healthyNodesPercentage = computed(() => {
    if (totalNodes.value === 0) return 0
    return Math.round((onlineNodes.value / totalNodes.value) * 100)
  })

  const criticalAlerts = computed(() => {
    return nodes.value.filter(node => {
      return node.responseTime > monitorConfig.alertThreshold.responseTime ||
             node.errorRate > monitorConfig.alertThreshold.errorRate ||
             node.memoryUsage > monitorConfig.alertThreshold.memoryUsage
    })
  })

  const versionSummary = computed(() => {
    const versions = {}
    nodes.value.forEach(node => {
      const version = node.version || 'unknown'
      versions[version] = (versions[version] || 0) + 1
    })
    return versions
  })

  const latestVersion = computed(() => {
    const versions = Object.keys(versionSummary.value)
      .filter(v => v !== 'unknown')
      .sort((a, b) => b.localeCompare(a, undefined, { numeric: true }))
    return versions[0] || 'unknown'
  })

  const outdatedNodes = computed(() => {
    return nodes.value.filter(node => 
      node.version && node.version !== latestVersion.value
    )
  })

  // 获取节点列表
  const fetchNodes = async () => {
    loading.value = true
    try {
      // 模拟API调用
      const mockNodes = [
        {
          id: 'hospital-001',
          name: '北京协和医院',
          ip: '*************',
          port: 8080,
          status: 'online',
          version: '1.2.3',
          lastHeartbeat: new Date().toISOString(),
          responseTime: 120,
          errorRate: 0.02,
          memoryUsage: 0.65,
          cpuUsage: 0.45,
          diskUsage: 0.30,
          rulesCount: 156,
          lastSyncTime: new Date(Date.now() - 300000).toISOString() // 5分钟前
        },
        {
          id: 'hospital-002',
          name: '上海华山医院',
          ip: '*************',
          port: 8080,
          status: 'warning',
          version: '1.2.2',
          lastHeartbeat: new Date(Date.now() - 120000).toISOString(), // 2分钟前
          responseTime: 3500,
          errorRate: 0.08,
          memoryUsage: 0.85,
          cpuUsage: 0.78,
          diskUsage: 0.55,
          rulesCount: 142,
          lastSyncTime: new Date(Date.now() - 1800000).toISOString() // 30分钟前
        },
        {
          id: 'hospital-003',
          name: '广州中山医院',
          ip: '*************',
          port: 8080,
          status: 'offline',
          version: '1.1.8',
          lastHeartbeat: new Date(Date.now() - 600000).toISOString(), // 10分钟前
          responseTime: 0,
          errorRate: 1.0,
          memoryUsage: 0,
          cpuUsage: 0,
          diskUsage: 0,
          rulesCount: 0,
          lastSyncTime: new Date(Date.now() - 3600000).toISOString() // 1小时前
        }
      ]

      nodes.value = mockNodes
      lastUpdateTime.value = new Date().toISOString()
      return mockNodes
    } catch (error) {
      console.error('获取节点列表失败:', error)
      ElMessage.error('获取节点列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 检查单个节点健康状态
  const checkNodeHealth = async (nodeId) => {
    const node = nodes.value.find(n => n.id === nodeId)
    if (!node) return null

    try {
      // 模拟健康检查API调用
      const healthData = {
        status: Math.random() > 0.2 ? 'online' : 'offline',
        responseTime: Math.floor(Math.random() * 2000) + 100,
        memoryUsage: Math.random() * 0.9,
        cpuUsage: Math.random() * 0.8,
        diskUsage: Math.random() * 0.7,
        lastHeartbeat: new Date().toISOString()
      }

      // 更新节点状态
      Object.assign(node, healthData)
      
      return healthData
    } catch (error) {
      console.error(`节点 ${nodeId} 健康检查失败:`, error)
      return null
    }
  }

  // 批量健康检查
  const checkAllNodesHealth = async () => {
    const promises = nodes.value.map(node => checkNodeHealth(node.id))
    const results = await Promise.allSettled(promises)
    
    const successCount = results.filter(r => r.status === 'fulfilled').length
    ElMessage.success(`健康检查完成，${successCount}/${nodes.value.length} 个节点检查成功`)
    
    return results
  }

  // 同步规则到节点
  const syncRulesToNode = async (nodeId) => {
    const node = nodes.value.find(n => n.id === nodeId)
    if (!node) {
      ElMessage.error('节点不存在')
      return false
    }

    try {
      // 模拟规则同步API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 更新节点同步时间
      node.lastSyncTime = new Date().toISOString()
      node.rulesCount = Math.floor(Math.random() * 200) + 100
      
      ElMessage.success(`规则同步到 ${node.name} 成功`)
      return true
    } catch (error) {
      console.error(`规则同步到节点 ${nodeId} 失败:`, error)
      ElMessage.error(`规则同步失败: ${error.message}`)
      return false
    }
  }

  // 批量同步规则
  const syncRulesToAllNodes = async () => {
    const onlineNodeIds = nodes.value
      .filter(node => node.status === 'online')
      .map(node => node.id)

    if (onlineNodeIds.length === 0) {
      ElMessage.warning('没有在线节点可以同步')
      return
    }

    const promises = onlineNodeIds.map(nodeId => syncRulesToNode(nodeId))
    const results = await Promise.allSettled(promises)
    
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length
    ElMessage.success(`规则同步完成，${successCount}/${onlineNodeIds.length} 个节点同步成功`)
  }

  // 重启节点
  const restartNode = async (nodeId) => {
    const node = nodes.value.find(n => n.id === nodeId)
    if (!node) {
      ElMessage.error('节点不存在')
      return false
    }

    try {
      // 模拟重启API调用
      node.status = 'restarting'
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // 重启后状态
      node.status = 'online'
      node.lastHeartbeat = new Date().toISOString()
      node.memoryUsage = Math.random() * 0.5 // 重启后内存使用率较低
      node.cpuUsage = Math.random() * 0.3
      
      ElMessage.success(`${node.name} 重启成功`)
      return true
    } catch (error) {
      console.error(`节点 ${nodeId} 重启失败:`, error)
      node.status = 'error'
      ElMessage.error(`节点重启失败: ${error.message}`)
      return false
    }
  }

  // 获取节点详细信息
  const getNodeDetails = (nodeId) => {
    return nodes.value.find(n => n.id === nodeId)
  }

  // 获取节点状态颜色
  const getNodeStatusColor = (status) => {
    const colorMap = {
      'online': '#67C23A',
      'offline': '#F56C6C',
      'warning': '#E6A23C',
      'restarting': '#409EFF',
      'error': '#F56C6C'
    }
    return colorMap[status] || '#909399'
  }

  // 格式化时间差
  const formatTimeDiff = (timestamp) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diff = now - time
    
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (days > 0) return `${days}天前`
    if (hours > 0) return `${hours}小时前`
    if (minutes > 0) return `${minutes}分钟前`
    return '刚刚'
  }

  // 格式化百分比
  const formatPercentage = (value) => {
    return `${Math.round(value * 100)}%`
  }

  return {
    // 状态
    nodes,
    loading,
    lastUpdateTime,
    monitorConfig,

    // 计算属性
    totalNodes,
    onlineNodes,
    offlineNodes,
    warningNodes,
    healthyNodesPercentage,
    criticalAlerts,
    versionSummary,
    latestVersion,
    outdatedNodes,

    // 方法
    fetchNodes,
    checkNodeHealth,
    checkAllNodesHealth,
    syncRulesToNode,
    syncRulesToAllNodes,
    restartNode,
    getNodeDetails,
    getNodeStatusColor,
    formatTimeDiff,
    formatPercentage
  }
}
