"""
规则数据迁移服务
负责将 rule_data_sets 表中的 JSON 数据迁移到新的 rule_details 表
"""

from datetime import datetime
from decimal import Decimal, InvalidOperation
from typing import Any

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import MigrationStatusEnum, RuleDataSet, RuleDetail, RuleDetailStatusEnum


class RuleDataMigrationError(Exception):
    """数据迁移相关异常"""

    def __init__(self, message: str, dataset_id: int | None = None, details: dict | None = None):
        super().__init__(message)
        self.dataset_id = dataset_id
        self.details = details or {}


class RuleDataMigration:
    """规则数据迁移服务"""

    def __init__(self, session_factory, batch_size: int = 100, enable_validation: bool = True):
        """
        初始化迁移服务

        Args:
            session_factory: 数据库会话工厂
            batch_size: 批量处理大小
            enable_validation: 是否启用数据验证
        """
        self.session_factory = session_factory
        self.batch_size = batch_size
        self.enable_validation = enable_validation

        # 迁移统计信息
        self.migration_stats = {
            "total_datasets": 0,
            "processed_datasets": 0,
            "successful_datasets": 0,
            "failed_datasets": 0,
            "total_details": 0,
            "successful_details": 0,
            "failed_details": 0,
            "start_time": None,
            "end_time": None,
            "errors": [],
            "warnings": [],
        }

        logger.info(f"RuleDataMigration 初始化完成，批量大小: {batch_size}, 验证: {enable_validation}")

    def migrate_all_datasets(self, dry_run: bool = False) -> dict[str, Any]:
        """
        迁移所有待迁移的数据集

        Args:
            dry_run: 是否为试运行模式（不实际写入数据）

        Returns:
            Dict: 迁移结果统计
        """
        logger.info(f"开始迁移所有数据集... (试运行: {dry_run})")

        # 重置统计信息
        self.migration_stats.update(
            {
                "total_datasets": 0,
                "processed_datasets": 0,
                "successful_datasets": 0,
                "failed_datasets": 0,
                "total_details": 0,
                "successful_details": 0,
                "failed_details": 0,
                "start_time": datetime.now(),
                "end_time": None,
                "errors": [],
                "warnings": [],
            }
        )

        try:
            with self.session_factory() as session:
                # 获取所有待迁移的数据集
                datasets = (
                    session.query(RuleDataSet).filter(RuleDataSet.migration_status == MigrationStatusEnum.PENDING).all()
                )

                self.migration_stats["total_datasets"] = len(datasets)
                logger.info(f"找到 {len(datasets)} 个待迁移的数据集")

                for dataset in datasets:
                    try:
                        detail_count = self.migrate_single_dataset(session, dataset)
                        dataset.migration_status = MigrationStatusEnum.COMPLETED
                        dataset.migration_timestamp = datetime.now()
                        self.migration_stats["successful_datasets"] += 1
                        self.migration_stats["total_details"] += detail_count

                        logger.info(f"数据集 {dataset.id} 迁移成功，迁移了 {detail_count} 条明细")

                    except Exception as e:
                        dataset.migration_status = MigrationStatusEnum.FAILED
                        dataset.migration_timestamp = datetime.now()
                        self.migration_stats["failed_datasets"] += 1
                        error_msg = f"数据集 {dataset.id} 迁移失败: {str(e)}"
                        self.migration_stats["errors"].append(error_msg)
                        logger.error(error_msg, exc_info=True)

                    session.commit()

                self.migration_stats["end_time"] = datetime.now()
                duration = self.migration_stats["end_time"] - self.migration_stats["start_time"]

                logger.info(f"数据迁移完成，耗时: {duration.total_seconds():.2f}秒")
                logger.info(
                    f"成功: {self.migration_stats['successful_datasets']}," 
                    f"失败: {self.migration_stats['failed_datasets']}, "
                    f"总明细数: {self.migration_stats['total_details']}"
                )

                return self.migration_stats

        except Exception as e:
            logger.error(f"数据迁移过程中发生严重错误: {e}", exc_info=True)
            raise RuleDataMigrationError(f"数据迁移失败: {str(e)}") from None

    def migrate_single_dataset(self, session: Session, dataset: RuleDataSet) -> int:
        """
        迁移单个数据集

        Args:
            session: 数据库会话
            dataset: 要迁移的数据集

        Returns:
            int: 迁移的明细数量
        """
        if not dataset.data_set:
            logger.warning(f"数据集 {dataset.id} 的 data_set 为空，跳过迁移")
            return 0

        if not isinstance(dataset.data_set, list):
            raise RuleDataMigrationError(
                f"数据集 {dataset.id} 的 data_set 不是列表格式: {type(dataset.data_set)}", dataset_id=dataset.id
            )

        # 更新迁移状态
        dataset.migration_status = MigrationStatusEnum.IN_PROGRESS
        session.commit()

        rule_details = []
        for i, item in enumerate(dataset.data_set):
            if not isinstance(item, dict):
                logger.warning(f"数据集 {dataset.id} 第 {i} 项不是字典格式，跳过")
                continue

            try:
                detail = self._create_rule_detail_from_json(dataset.id, item, dataset.uploaded_by)
                rule_details.append(detail)
            except Exception as e:
                logger.error(f"数据集 {dataset.id} 第 {i} 项转换失败: {e}")
                continue

        if rule_details:
            # 批量插入
            session.add_all(rule_details)
            session.flush()  # 确保数据写入数据库

        return len(rule_details)

    def _create_rule_detail_from_json(
        self, dataset_id: int, item: dict[str, Any], created_by: str | None
    ) -> RuleDetail:
        """
        从 JSON 数据创建 RuleDetail 对象

        Args:
            dataset_id: 数据集ID
            item: JSON 数据项
            created_by: 创建人

        Returns:
            RuleDetail: 创建的规则明细对象
        """
        detail = RuleDetail(
            dataset_id=dataset_id,
            rule_detail_id=item.get("rule_id"),
            rule_name=item.get("rule_name"),
            error_level_1=item.get("error_level_1"),
            error_level_2=item.get("error_level_2"),
            error_level_3=item.get("error_level_3"),
            error_reason=item.get("error_reason"),
            error_severity=item.get("error_severity"),
            quality_basis=item.get("quality_basis"),
            location_desc=item.get("location_desc"),
            prompt_field_type=item.get("prompt_field_type"),
            prompt_field_code=item.get("prompt_field_code"),
            prompt_field_seq=self._safe_int(item.get("prompt_field_seq")),
            rule_category=item.get("rule_category"),
            applicable_business=item.get("applicable_business"),
            applicable_region=item.get("applicable_region"),
            default_selected=self._safe_bool(item.get("default_selected")),
            involved_amount=self._safe_decimal(item.get("involved_amount")),
            usage_quantity=self._safe_int(item.get("usage_quantity")),
            violation_quantity=self._safe_int(item.get("violation_quantity")),
            usage_days=self._safe_int(item.get("usage_days")),
            violation_days=self._safe_int(item.get("violation_days")),
            violation_items=item.get("violation_items"),
            effective_start_time=self._safe_datetime(item.get("effective_start_time")),
            effective_end_time=self._safe_datetime(item.get("effective_end_time")),
            remark=item.get("remark"),
            status=RuleDetailStatusEnum.ACTIVE,
            created_by=created_by,
        )

        return detail

    def _safe_int(self, value: Any) -> int | None:
        """安全转换为整数"""
        if value is None or value == "":
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None

    def _safe_bool(self, value: Any) -> bool:
        """安全转换为布尔值"""
        if value is None:
            return False
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ("true", "1", "yes", "on", "是", "真")
        try:
            return bool(int(value))
        except (ValueError, TypeError):
            return False

    def _safe_decimal(self, value: Any) -> Decimal | None:
        """安全转换为 Decimal"""
        if value is None or value == "":
            return None
        try:
            return Decimal(str(value))
        except (InvalidOperation, ValueError, TypeError):
            return None

    def _safe_datetime(self, value: Any) -> datetime | None:
        """安全转换为 datetime"""
        if value is None or value == "":
            return None

        if isinstance(value, datetime):
            return value

        if isinstance(value, str):
            # 尝试多种时间格式
            formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M",
                "%Y-%m-%d",
                "%Y/%m/%d %H:%M:%S",
                "%Y/%m/%d %H:%M",
                "%Y/%m/%d",
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(value, fmt)
                except ValueError:
                    continue

        # 尝试时间戳转换
        try:
            timestamp = float(value)
            # 处理毫秒时间戳
            if timestamp > 1e10:
                timestamp = timestamp / 1000
            return datetime.fromtimestamp(timestamp)
        except (ValueError, TypeError, OSError):
            pass

        return None

    def get_migration_status(self) -> dict[str, Any]:
        """
        获取迁移状态统计

        Returns:
            Dict: 迁移状态统计信息
        """
        with self.session_factory() as session:
            total = session.query(RuleDataSet).count()
            pending = (
                session.query(RuleDataSet).filter(RuleDataSet.migration_status == MigrationStatusEnum.PENDING).count()
            )
            in_progress = (
                session.query(RuleDataSet)
                .filter(RuleDataSet.migration_status == MigrationStatusEnum.IN_PROGRESS)
                .count()
            )
            completed = (
                session.query(RuleDataSet).filter(RuleDataSet.migration_status == MigrationStatusEnum.COMPLETED).count()
            )
            failed = (
                session.query(RuleDataSet).filter(RuleDataSet.migration_status == MigrationStatusEnum.FAILED).count()
            )

            return {
                "total": total,
                "pending": pending,
                "in_progress": in_progress,
                "completed": completed,
                "failed": failed,
                "completion_rate": (completed / total * 100) if total > 0 else 0,
            }
