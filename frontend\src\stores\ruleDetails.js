import { ref, computed, nextTick } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import * as ruleDetailsAPI from '@/api/ruleDetails'

/**
 * 规则明细管理 Store
 * 专门处理规则明细的 CRUD 操作、批量处理、搜索统计等功能
 */
export const useRuleDetailsStore = defineStore('ruleDetails', () => {
  // ==================== 核心状态定义 ====================

  // 明细列表相关状态
  const detailsList = ref([])           // 当前明细列表
  const currentDetail = ref(null)       // 当前选中的明细
  const selectedDetails = ref([])       // 批量选中的明细

  // 分页和过滤状态
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    hasNext: false,
    hasPrev: false
  })

  const filters = ref({
    status: null,
    search: '',
    errorLevel1: null,
    errorLevel2: null,
    ruleCategory: null,
    sortBy: 'created_at',
    sortOrder: 'desc'
  })

  // 操作状态
  const operationStatus = ref({
    lastOperation: null,
    operationTime: null,
    affectedCount: 0,
    errors: []
  })

  // 加载状态
  const loading = ref(false)
  const detailLoading = ref(false)
  const batchLoading = ref(false)
  const searchLoading = ref(false)

  // ==================== 缓存管理 ====================

  // 多级缓存
  const detailsCache = ref(new Map())   // 明细列表缓存
  const detailCache = ref(new Map())    // 单条明细缓存
  const searchCache = ref(new Map())    // 搜索结果缓存
  const statsCache = ref(new Map())     // 统计数据缓存

  // 缓存配置
  const CACHE_EXPIRE_TIME = 30 * 60 * 1000 // 30分钟
  const MAX_CACHE_SIZE = 100

  // ==================== 计算属性 ====================

  // 基础统计
  const detailsCount = computed(() => detailsList.value.length)
  const hasDetails = computed(() => detailsList.value.length > 0)
  const selectedCount = computed(() => selectedDetails.value.length)
  const hasSelected = computed(() => selectedDetails.value.length > 0)

  // 加载状态
  const isLoading = computed(() =>
    loading.value || detailLoading.value || batchLoading.value || searchLoading.value
  )

  // 分页信息
  const paginationInfo = computed(() => ({
    current: pagination.value.page,
    total: pagination.value.total,
    pageSize: pagination.value.pageSize,
    showTotal: `共 ${pagination.value.total} 条记录`,
    showSizeChanger: true,
    showQuickJumper: true
  }))

  // 过滤器摘要
  const activeFilters = computed(() => {
    const active = []
    if (filters.value.status) active.push(`状态: ${filters.value.status}`)
    if (filters.value.search) active.push(`搜索: ${filters.value.search}`)
    if (filters.value.errorLevel1) active.push(`错误类型: ${filters.value.errorLevel1}`)
    if (filters.value.ruleCategory) active.push(`规则类别: ${filters.value.ruleCategory}`)
    return active
  })

  // ==================== 缓存工具方法 ====================

  /**
   * 生成缓存键
   */
  const generateCacheKey = (type, ...params) => {
    switch (type) {
      case 'list':
        return `details:${params[0]}:${JSON.stringify(params[1])}`
      case 'detail':
        return `detail:${params[0]}`
      case 'search':
        try {
          return `search:${params[0]}:${btoa(encodeURIComponent(JSON.stringify(params[1])))}`
        } catch (error) {
          // 如果 btoa 失败，使用简单的哈希
          return `search:${params[0]}:${JSON.stringify(params[1]).replace(/[^a-zA-Z0-9]/g, '_')}`
        }
      case 'stats':
        return `stats:${params[0]}`
      default:
        return `${type}:${params.join(':')}`
    }
  }

  /**
   * 检查缓存是否过期
   */
  const isCacheExpired = (cacheItem) => {
    if (!cacheItem || !cacheItem.timestamp) return true
    return Date.now() - cacheItem.timestamp > CACHE_EXPIRE_TIME
  }

  /**
   * 设置缓存
   */
  const setCache = (cache, key, data) => {
    // 限制缓存大小
    if (cache.size >= MAX_CACHE_SIZE) {
      const firstKey = cache.keys().next().value
      cache.delete(firstKey)
    }

    cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 获取缓存
   */
  const getCache = (cache, key) => {
    const cacheItem = cache.get(key)
    if (!cacheItem || isCacheExpired(cacheItem)) {
      cache.delete(key)
      return null
    }
    return cacheItem.data
  }

  // ==================== API 调用封装 ====================

  /**
   * 验证API响应格式并提取数据
   * @param {Object} response - API响应
   * @returns {Object} 标准化的数据对象
   */
  const normalizeApiResponse = (response) => {
    if (!response) return null

    // 统一API响应格式：{success: true, data: {...}}
    if (response.success && response.data) {
      return response.data
    }

    // 兼容旧格式：直接返回数据
    if (response.items || Array.isArray(response)) {
      return response
    }

    console.warn('未知的API响应格式:', response)
    return null
  }

  /**
   * 统一的 API 调用封装
   */
  const apiCall = async (apiMethod, loadingRef, ...args) => {
    try {
      if (loadingRef) loadingRef.value = true
      const result = await apiMethod(...args)
      return result
    } catch (error) {
      console.error('API调用失败:', error)
      ElMessage.error(error.message || 'API调用失败')
      throw error
    } finally {
      if (loadingRef) loadingRef.value = false
    }
  }

  // ==================== 核心 CRUD 操作 ====================

  /**
   * 获取规则明细列表
   * @param {string} ruleKey - 规则键
   * @param {Object} params - 查询参数
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 明细列表数据
   */
  const fetchDetailsList = async (ruleKey, params = {}, useCache = true) => {
    if (!ruleKey) return null

    const queryParams = { ...filters.value, ...params }
    const cacheKey = generateCacheKey('list', ruleKey, queryParams)

    // 检查缓存
    if (useCache) {
      const cached = getCache(detailsCache.value, cacheKey)
      if (cached) {
        detailsList.value = cached.items || []
        pagination.value = { ...pagination.value, ...cached.pagination }
        return cached
      }
    }

    // API 调用
    const result = await apiCall(
      ruleDetailsAPI.getRuleDetailsList,
      loading,
      ruleKey,
      queryParams
    )

    if (result) {
      // 标准化API响应数据
      const normalizedData = normalizeApiResponse(result)

      if (normalizedData) {
        // 更新状态 - 修正数据结构解析
        detailsList.value = normalizedData.items || []
        pagination.value = {
          page: normalizedData.page || 1,
          pageSize: normalizedData.page_size || 20,
          total: normalizedData.total || 0,
          hasNext: normalizedData.has_next || false,
          hasPrev: normalizedData.has_prev || false
        }

        // 更新缓存 - 缓存标准化后的数据
        setCache(detailsCache.value, cacheKey, normalizedData)

        // 同时缓存单条明细
        if (normalizedData.items) {
          normalizedData.items.forEach(detail => {
            const detailKey = generateCacheKey('detail', detail.id)
            setCache(detailCache.value, detailKey, detail)
          })
        }
      } else {
        console.warn('fetchDetailsList: 无法解析API响应数据', result)
        detailsList.value = []
        pagination.value = {
          page: 1,
          pageSize: 20,
          total: 0,
          hasNext: false,
          hasPrev: false
        }
      }
    }

    return result
  }

  /**
   * 获取单条规则明细
   * @param {string} ruleKey - 规则键
   * @param {number|string} detailId - 明细ID
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 明细数据
   */
  const fetchDetailById = async (ruleKey, detailId, useCache = true) => {
    if (!ruleKey || !detailId) return null

    const cacheKey = generateCacheKey('detail', detailId)

    // 检查缓存
    if (useCache) {
      const cached = getCache(detailCache.value, cacheKey)
      if (cached) {
        currentDetail.value = cached
        return cached
      }
    }

    // API 调用
    const result = await apiCall(
      ruleDetailsAPI.getRuleDetailById,
      detailLoading,
      ruleKey,
      detailId
    )

    if (result) {
      // 标准化API响应数据
      const normalizedData = normalizeApiResponse(result)
      const detailData = normalizedData || result

      currentDetail.value = detailData
      setCache(detailCache.value, cacheKey, detailData)
    }

    return result
  }

  /**
   * 创建规则明细
   * @param {string} ruleKey - 规则键
   * @param {Object} detailData - 明细数据
   * @returns {Promise<Object>} 创建结果
   */
  const createDetail = async (ruleKey, detailData) => {
    if (!ruleKey || !detailData) return null

    const result = await apiCall(
      ruleDetailsAPI.createRuleDetail,
      loading,
      ruleKey,
      detailData
    )

    if (result) {
      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'create',
        operationTime: Date.now(),
        affectedCount: 1,
        errors: []
      }

      // 清除相关缓存
      clearRuleCache(ruleKey)

      ElMessage.success('创建明细成功')
    }

    return result
  }

  /**
   * 更新规则明细
   * @param {string} ruleKey - 规则键
   * @param {number|string} detailId - 明细ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  const updateDetail = async (ruleKey, detailId, updateData) => {
    if (!ruleKey || !detailId || !updateData) return null

    const result = await apiCall(
      ruleDetailsAPI.updateRuleDetail,
      loading,
      ruleKey,
      detailId,
      updateData
    )

    if (result) {
      // 更新本地状态
      const index = detailsList.value.findIndex(d => d.id === detailId)
      if (index !== -1) {
        detailsList.value[index] = { ...detailsList.value[index], ...result }
      }

      if (currentDetail.value && currentDetail.value.id === detailId) {
        currentDetail.value = { ...currentDetail.value, ...result }
      }

      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'update',
        operationTime: Date.now(),
        affectedCount: 1,
        errors: []
      }

      // 清除相关缓存
      clearDetailCache(detailId)
      clearRuleCache(ruleKey)

      ElMessage.success('更新明细成功')
    }

    return result
  }

  /**
   * 删除规则明细
   * @param {string} ruleKey - 规则键
   * @param {number|string} detailId - 明细ID
   * @returns {Promise<boolean>} 删除结果
   */
  const deleteDetail = async (ruleKey, detailId) => {
    if (!ruleKey || !detailId) return false

    const result = await apiCall(
      ruleDetailsAPI.deleteRuleDetail,
      loading,
      ruleKey,
      detailId
    )

    if (result) {
      // 更新本地状态
      detailsList.value = detailsList.value.filter(d => d.id !== detailId)

      if (currentDetail.value && currentDetail.value.id === detailId) {
        currentDetail.value = null
      }

      // 从选中列表中移除
      selectedDetails.value = selectedDetails.value.filter(d => d.id !== detailId)

      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'delete',
        operationTime: Date.now(),
        affectedCount: 1,
        errors: []
      }

      // 清除相关缓存
      clearDetailCache(detailId)
      clearRuleCache(ruleKey)

      ElMessage.success('删除明细成功')
    }

    return result
  }

  // ==================== 批量操作方法 ====================

  /**
   * 批量创建规则明细
   * @param {string} ruleKey - 规则键
   * @param {Array} detailsData - 明细数据数组
   * @returns {Promise<Object>} 批量创建结果
   */
  const batchCreateDetails = async (ruleKey, detailsData) => {
    if (!ruleKey || !Array.isArray(detailsData) || detailsData.length === 0) {
      return null
    }

    const result = await apiCall(
      ruleDetailsAPI.batchCreateRuleDetails,
      batchLoading,
      ruleKey,
      detailsData
    )

    if (result) {
      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'batchCreate',
        operationTime: Date.now(),
        affectedCount: result.successCount || 0,
        errors: result.errors || []
      }

      // 清除相关缓存
      clearRuleCache(ruleKey)

      const message = result.errors && result.errors.length > 0
        ? `批量创建完成，成功 ${result.successCount} 条，失败 ${result.errors.length} 条`
        : `批量创建成功，共 ${result.successCount} 条`

      ElMessage.success(message)
    }

    return result
  }

  /**
   * 批量更新规则明细
   * @param {string} ruleKey - 规则键
   * @param {Array} updates - 更新数据数组
   * @returns {Promise<Object>} 批量更新结果
   */
  const batchUpdateDetails = async (ruleKey, updates) => {
    if (!ruleKey || !Array.isArray(updates) || updates.length === 0) {
      return null
    }

    // 乐观更新：先更新本地状态
    const originalDetails = [...detailsList.value]

    try {
      // 应用乐观更新
      updates.forEach(update => {
        const index = detailsList.value.findIndex(d => d.id === update.id)
        if (index !== -1) {
          detailsList.value[index] = { ...detailsList.value[index], ...update.data }
        }
      })

      const result = await apiCall(
        ruleDetailsAPI.batchUpdateRuleDetails,
        batchLoading,
        ruleKey,
        updates
      )

      if (result) {
        // 同步服务器结果
        if (result.updatedDetails) {
          result.updatedDetails.forEach(detail => {
            const index = detailsList.value.findIndex(d => d.id === detail.id)
            if (index !== -1) {
              detailsList.value[index] = detail
            }
          })
        }

        // 更新操作状态
        operationStatus.value = {
          lastOperation: 'batchUpdate',
          operationTime: Date.now(),
          affectedCount: result.successCount || 0,
          errors: result.errors || []
        }

        // 清除相关缓存
        clearRuleCache(ruleKey)

        const message = result.errors && result.errors.length > 0
          ? `批量更新完成，成功 ${result.successCount} 条，失败 ${result.errors.length} 条`
          : `批量更新成功，共 ${result.successCount} 条`

        ElMessage.success(message)
      }

      return result
    } catch (error) {
      // 回滚乐观更新
      detailsList.value = originalDetails
      throw error
    }
  }

  /**
   * 批量删除规则明细
   * @param {string} ruleKey - 规则键
   * @param {Array} detailIds - 明细ID数组
   * @returns {Promise<Object>} 批量删除结果
   */
  const batchDeleteDetails = async (ruleKey, detailIds) => {
    if (!ruleKey || !Array.isArray(detailIds) || detailIds.length === 0) {
      return null
    }

    const result = await apiCall(
      ruleDetailsAPI.batchDeleteRuleDetails,
      batchLoading,
      ruleKey,
      detailIds
    )

    if (result) {
      // 更新本地状态
      detailsList.value = detailsList.value.filter(d => !detailIds.includes(d.id))
      selectedDetails.value = selectedDetails.value.filter(d => !detailIds.includes(d.id))

      if (currentDetail.value && detailIds.includes(currentDetail.value.id)) {
        currentDetail.value = null
      }

      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'batchDelete',
        operationTime: Date.now(),
        affectedCount: result.successCount || 0,
        errors: result.errors || []
      }

      // 清除相关缓存
      detailIds.forEach(id => clearDetailCache(id))
      clearRuleCache(ruleKey)

      const message = result.errors && result.errors.length > 0
        ? `批量删除完成，成功 ${result.successCount} 条，失败 ${result.errors.length} 条`
        : `批量删除成功，共 ${result.successCount} 条`

      ElMessage.success(message)
    }

    return result
  }

  // ==================== 搜索和过滤方法 ====================

  /**
   * 搜索规则明细
   * @param {string} ruleKey - 规则键
   * @param {Object} searchParams - 搜索参数
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 搜索结果
   */
  const searchDetails = async (ruleKey, searchParams = {}, useCache = true) => {
    if (!ruleKey) return null

    const cacheKey = generateCacheKey('search', ruleKey, searchParams)

    // 检查缓存
    if (useCache) {
      const cached = getCache(searchCache.value, cacheKey)
      if (cached) {
        detailsList.value = cached.items || []
        pagination.value = {
          page: cached.page || 1,
          pageSize: cached.page_size || 20,
          total: cached.total || 0,
          hasNext: cached.has_next || false,
          hasPrev: cached.has_prev || false
        }
        return cached
      }
    }

    // API 调用
    const result = await apiCall(
      ruleDetailsAPI.searchRuleDetails,
      searchLoading,
      ruleKey,
      searchParams
    )

    if (result) {
      // 标准化API响应数据
      const normalizedData = normalizeApiResponse(result)

      if (normalizedData) {
        // 更新状态 - 修正数据结构解析
        detailsList.value = normalizedData.items || []
        pagination.value = {
          page: normalizedData.page || 1,
          pageSize: normalizedData.page_size || 20,
          total: normalizedData.total || 0,
          hasNext: normalizedData.has_next || false,
          hasPrev: normalizedData.has_prev || false
        }

        // 更新缓存 - 缓存标准化后的数据
        setCache(searchCache.value, cacheKey, normalizedData)
      } else {
        console.warn('searchDetails: 无法解析API响应数据', result)
        detailsList.value = []
        pagination.value = {
          page: 1,
          pageSize: 20,
          total: 0,
          hasNext: false,
          hasPrev: false
        }
      }
    }

    return result
  }

  /**
   * 更新过滤条件
   * @param {Object} newFilters - 新的过滤条件
   */
  const updateFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  /**
   * 重置过滤条件
   */
  const resetFilters = () => {
    filters.value = {
      status: null,
      search: '',
      errorLevel1: null,
      errorLevel2: null,
      ruleCategory: null,
      sortBy: 'created_at',
      sortOrder: 'desc'
    }
  }

  /**
   * 更新分页信息
   * @param {Object} newPagination - 新的分页信息
   */
  const updatePagination = (newPagination) => {
    pagination.value = { ...pagination.value, ...newPagination }
  }

  // ==================== 选择管理方法 ====================

  /**
   * 选择单条明细
   * @param {Object} detail - 明细对象
   */
  const selectDetail = (detail) => {
    if (!selectedDetails.value.find(d => d.id === detail.id)) {
      selectedDetails.value.push(detail)
    }
  }

  /**
   * 取消选择单条明细
   * @param {Object} detail - 明细对象
   */
  const unselectDetail = (detail) => {
    selectedDetails.value = selectedDetails.value.filter(d => d.id !== detail.id)
  }

  /**
   * 切换明细选择状态
   * @param {Object} detail - 明细对象
   */
  const toggleDetailSelection = (detail) => {
    const isSelected = selectedDetails.value.find(d => d.id === detail.id)
    if (isSelected) {
      unselectDetail(detail)
    } else {
      selectDetail(detail)
    }
  }

  /**
   * 全选/取消全选
   * @param {boolean} selectAll - 是否全选
   */
  const toggleSelectAll = (selectAll) => {
    if (selectAll) {
      selectedDetails.value = [...detailsList.value]
    } else {
      selectedDetails.value = []
    }
  }

  /**
   * 清除所有选择
   */
  const clearSelection = () => {
    selectedDetails.value = []
  }

  // ==================== 缓存管理方法 ====================

  /**
   * 清除指定明细的缓存
   * @param {number|string} detailId - 明细ID
   */
  const clearDetailCache = (detailId) => {
    const cacheKey = generateCacheKey('detail', detailId)
    detailCache.value.delete(cacheKey)
  }

  /**
   * 清除指定规则的所有相关缓存
   * @param {string} ruleKey - 规则键
   */
  const clearRuleCache = (ruleKey) => {
    // 清除列表缓存
    for (const [key] of detailsCache.value) {
      if (key.includes(ruleKey)) {
        detailsCache.value.delete(key)
      }
    }

    // 清除搜索缓存
    for (const [key] of searchCache.value) {
      if (key.includes(ruleKey)) {
        searchCache.value.delete(key)
      }
    }

    // 清除统计缓存
    const statsKey = generateCacheKey('stats', ruleKey)
    statsCache.value.delete(statsKey)
  }

  /**
   * 清除所有缓存
   */
  const clearAllCache = () => {
    detailsCache.value.clear()
    detailCache.value.clear()
    searchCache.value.clear()
    statsCache.value.clear()
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  const getCacheStats = () => ({
    detailsCache: detailsCache.value.size,
    detailCache: detailCache.value.size,
    searchCache: searchCache.value.size,
    statsCache: statsCache.value.size,
    totalSize: detailsCache.value.size + detailCache.value.size +
      searchCache.value.size + statsCache.value.size
  })

  // ==================== 状态管理方法 ====================

  /**
   * 设置当前明细
   * @param {Object} detail - 明细对象
   */
  const setCurrentDetail = (detail) => {
    currentDetail.value = detail
  }

  /**
   * 清除当前明细
   */
  const clearCurrentDetail = () => {
    currentDetail.value = null
  }

  /**
   * 重置整个 Store 状态
   */
  const resetStore = () => {
    detailsList.value = []
    currentDetail.value = null
    selectedDetails.value = []

    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0,
      hasNext: false,
      hasPrev: false
    }

    resetFilters()

    operationStatus.value = {
      lastOperation: null,
      operationTime: null,
      affectedCount: 0,
      errors: []
    }

    loading.value = false
    detailLoading.value = false
    batchLoading.value = false
    searchLoading.value = false

    clearAllCache()
  }

  // ==================== 返回公共接口 ====================

  return {
    // 核心状态
    detailsList,
    currentDetail,
    selectedDetails,
    pagination,
    filters,
    operationStatus,

    // 加载状态
    loading,
    detailLoading,
    batchLoading,
    searchLoading,

    // 计算属性
    detailsCount,
    hasDetails,
    selectedCount,
    hasSelected,
    isLoading,
    paginationInfo,
    activeFilters,

    // 核心 CRUD 操作
    fetchDetailsList,
    fetchDetailById,
    createDetail,
    updateDetail,
    deleteDetail,

    // 批量操作
    batchCreateDetails,
    batchUpdateDetails,
    batchDeleteDetails,

    // 搜索和过滤
    searchDetails,
    updateFilters,
    resetFilters,
    updatePagination,

    // 选择管理
    selectDetail,
    unselectDetail,
    toggleDetailSelection,
    toggleSelectAll,
    clearSelection,

    // 缓存管理
    clearDetailCache,
    clearRuleCache,
    clearAllCache,
    getCacheStats,

    // 状态管理
    setCurrentDetail,
    clearCurrentDetail,
    resetStore
  }
})
