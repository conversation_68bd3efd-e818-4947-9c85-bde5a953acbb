<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-icon">
        <el-icon :size="120" color="#909399">
          <QuestionFilled />
        </el-icon>
      </div>
      
      <div class="error-content">
        <h1 class="error-title">404</h1>
        <h2 class="error-subtitle">页面未找到</h2>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移动。请检查URL是否正确，或返回首页继续浏览。
        </p>
        
        <div class="error-details" v-if="showDetails">
          <el-alert
            title="页面信息"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p><strong>请求路径:</strong> {{ currentPath }}</p>
              <p><strong>来源页面:</strong> {{ fromPath || '直接访问' }}</p>
              <p><strong>时间:</strong> {{ currentTime }}</p>
            </template>
          </el-alert>
        </div>
        
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          
          <el-button @click="goBack" v-if="canGoBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
          
          <el-button @click="searchSuggestions" type="success" plain>
            <el-icon><Search /></el-icon>
            搜索建议
          </el-button>
          
          <el-button @click="toggleDetails" text>
            {{ showDetails ? '隐藏详情' : '显示详情' }}
          </el-button>
        </div>
        
        <!-- 推荐页面 -->
        <div class="suggestions" v-if="showSuggestions">
          <h3>您可能想访问：</h3>
          <div class="suggestion-list">
            <el-button
              v-for="suggestion in suggestions"
              :key="suggestion.path"
              @click="goToSuggestion(suggestion.path)"
              text
              type="primary"
            >
              {{ suggestion.title }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { QuestionFilled, House, Back, Search } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const showDetails = ref(false)
const showSuggestions = ref(false)
const fromPath = ref('')

// 计算属性
const currentPath = computed(() => route.fullPath)
const currentTime = computed(() => new Date().toLocaleString())
const canGoBack = computed(() => window.history.length > 1)

// 推荐页面
const suggestions = ref([
  { title: '模板状态仪表盘', path: '/' },
  { title: '规则配置管理', path: '/rules/management' },
  { title: 'API连接测试', path: '/test' }
])

// 方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (canGoBack.value) {
    router.go(-1)
  } else {
    goHome()
  }
}

const searchSuggestions = () => {
  showSuggestions.value = !showSuggestions.value
}

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const goToSuggestion = (path) => {
  router.push(path)
}

// 生命周期
onMounted(() => {
  // 记录来源页面
  if (document.referrer) {
    try {
      const referrerUrl = new URL(document.referrer)
      fromPath.value = referrerUrl.pathname
    } catch (e) {
      fromPath.value = document.referrer
    }
  }
})
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.error-container {
  text-align: center;
  max-width: 600px;
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.error-icon {
  margin-bottom: 20px;
}

.error-title {
  font-size: 72px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1;
}

.error-subtitle {
  font-size: 24px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 16px;
}

.error-description {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 30px;
}

.error-details {
  margin: 20px 0;
  text-align: left;
}

.error-details p {
  margin: 8px 0;
  font-size: 14px;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.error-actions .el-button {
  min-width: 120px;
}

.suggestions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.suggestions h3 {
  font-size: 16px;
  color: #303133;
  margin-bottom: 16px;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    padding: 30px 20px;
  }
  
  .error-title {
    font-size: 48px;
  }
  
  .error-subtitle {
    font-size: 20px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
