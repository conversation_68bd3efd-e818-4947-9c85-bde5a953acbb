<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <div class="batch-operation-content">
      <!-- 操作确认信息 -->
      <div class="operation-info">
        <div class="info-header">
          <el-icon class="warning-icon" :size="20">
            <WarningFilled />
          </el-icon>
          <span class="info-title">{{ operationTitle }}</span>
        </div>
        <div class="info-description">
          {{ operationDescription }}
        </div>
      </div>

      <!-- 选中项目列表 -->
      <div class="selected-items">
        <div class="items-header">
          <span class="items-title">选中的明细项目 ({{ selectedDetails.length }} 项)</span>
          <el-button 
            type="text" 
            size="small" 
            @click="toggleShowAll"
          >
            {{ showAllItems ? '收起' : '展开全部' }}
          </el-button>
        </div>
        
        <div class="items-list" :class="{ 'collapsed': !showAllItems }">
          <div 
            v-for="(detail, index) in displayedItems" 
            :key="detail.id"
            class="item-row"
          >
            <div class="item-info">
              <span class="item-id">{{ detail.rule_detail_id }}</span>
              <span class="item-name">{{ detail.rule_name }}</span>
              <StatusTag :status="detail.status" size="small" />
            </div>
          </div>
          
          <div v-if="!showAllItems && selectedDetails.length > 5" class="more-items">
            还有 {{ selectedDetails.length - 5 }} 项...
          </div>
        </div>
      </div>

      <!-- 操作选项 -->
      <div v-if="operationType === 'activate' || operationType === 'deactivate'" class="operation-options">
        <div class="options-title">操作选项</div>
        <el-checkbox v-model="updateRelated">同时更新相关依赖项</el-checkbox>
        <el-checkbox v-model="sendNotification">发送操作通知</el-checkbox>
      </div>

      <!-- 删除确认 -->
      <div v-if="operationType === 'delete'" class="delete-confirmation">
        <div class="confirmation-title">删除确认</div>
        <el-alert
          title="警告：删除操作不可恢复"
          type="warning"
          :closable="false"
          show-icon
        />
        <div class="confirmation-input">
          <span class="input-label">请输入 "确认删除" 以继续：</span>
          <el-input 
            v-model="deleteConfirmText"
            placeholder="请输入确认文本"
            style="margin-top: 8px"
          />
        </div>
      </div>

      <!-- 进度显示 -->
      <div v-if="isProcessing || operationResult" class="progress-section">
        <BatchProgressMonitor
          :operation-type="operationType"
          :total-count="totalItems"
          :completed-count="processedCount"
          :success-count="successCount"
          :error-count="errorCount"
          :current-operation="progressText"
          :errors="operationErrors"
          :operation-steps="operationSteps"
          :current-step="currentStep"
          :show-steps="showDetailedProgress"
          :status="operationStatus"
          :start-time="operationStartTime"
          :can-pause="canPauseOperation"
          :can-resume="canResumeOperation"
          :can-cancel="canCancelOperation"
          :can-retry="canRetryOperation"
          @pause="handlePauseOperation"
          @resume="handleResumeOperation"
          @cancel="handleCancelOperation"
          @retry="handleRetryOperation"
          @export-result="handleExportResult"
        />
      </div>

      <!-- 操作结果 -->
      <div v-if="operationResult" class="result-section">
        <div class="result-title">操作结果</div>
        <el-alert
          :title="operationResult.title"
          :type="operationResult.type"
          :closable="false"
          show-icon
        >
          <div class="result-details">
            <div>成功：{{ operationResult.success }} 项</div>
            <div v-if="operationResult.failed > 0">失败：{{ operationResult.failed }} 项</div>
            <div v-if="operationResult.errors.length > 0" class="error-list">
              <div class="error-title">错误详情：</div>
              <ul>
                <li v-for="error in operationResult.errors" :key="error.id">
                  {{ error.name }}: {{ error.message }}
                </li>
              </ul>
            </div>
          </div>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="isProcessing">
          {{ operationResult ? '关闭' : '取消' }}
        </el-button>
        <el-button 
          v-if="!operationResult"
          type="primary" 
          @click="handleConfirm" 
          :loading="isProcessing"
          :disabled="!canConfirm"
        >
          {{ confirmButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { WarningFilled } from '@element-plus/icons-vue'
import StatusTag from '../common/StatusTag.vue'
import BatchProgressMonitor from './BatchProgressMonitor.vue'
import { batchUpdateRuleDetails, batchDeleteRuleDetails } from '../../api/ruleDetails'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ruleKey: {
    type: String,
    required: true
  },
  selectedDetails: {
    type: Array,
    default: () => []
  },
  operationType: {
    type: String,
    default: '', // 'activate' | 'deactivate' | 'delete'
    validator: (value) => ['', 'activate', 'deactivate', 'delete'].includes(value)
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success', 'cancel'])

// 响应式数据
const showAllItems = ref(false)
const updateRelated = ref(false)
const sendNotification = ref(true)
const deleteConfirmText = ref('')
const isProcessing = ref(false)
const progressPercentage = ref(0)
const progressStatus = ref('')
const progressText = ref('')
const operationResult = ref(null)

// 新增的进度监控数据
const processedCount = ref(0)
const successCount = ref(0)
const errorCount = ref(0)
const operationErrors = ref([])
const operationSteps = ref([])
const currentStep = ref(0)
const showDetailedProgress = ref(true)
const operationStatus = ref('idle')
const operationStartTime = ref(null)
const canPauseOperation = ref(false)
const canResumeOperation = ref(false)
const canCancelOperation = ref(true)
const canRetryOperation = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  const titleMap = {
    'activate': '批量激活规则明细',
    'deactivate': '批量停用规则明细',
    'delete': '批量删除规则明细'
  }
  return titleMap[props.operationType] || '批量操作'
})

const operationTitle = computed(() => {
  const titleMap = {
    'activate': '确认激活选中的规则明细',
    'deactivate': '确认停用选中的规则明细',
    'delete': '确认删除选中的规则明细'
  }
  return titleMap[props.operationType] || '确认操作'
})

const operationDescription = computed(() => {
  const descMap = {
    'activate': '激活后，这些规则明细将变为活跃状态，可以正常使用。',
    'deactivate': '停用后，这些规则明细将变为非活跃状态，暂停使用。',
    'delete': '删除后，这些规则明细将被永久删除，无法恢复。请谨慎操作。'
  }
  return descMap[props.operationType] || '请确认要执行的操作。'
})

const confirmButtonText = computed(() => {
  const textMap = {
    'activate': '确认激活',
    'deactivate': '确认停用',
    'delete': '确认删除'
  }
  return textMap[props.operationType] || '确认'
})

const displayedItems = computed(() => {
  return showAllItems.value ? props.selectedDetails : props.selectedDetails.slice(0, 5)
})

const canConfirm = computed(() => {
  if (props.operationType === 'delete') {
    return deleteConfirmText.value === '确认删除'
  }
  return props.selectedDetails.length > 0
})

// 初始化操作步骤
const initializeOperationSteps = () => {
  const stepTemplates = {
    activate: [
      { title: '准备激活', description: '验证选中项目状态', status: 'wait' },
      { title: '执行激活', description: '批量激活规则明细', status: 'wait' },
      { title: '更新缓存', description: '刷新相关缓存数据', status: 'wait' },
      { title: '完成操作', description: '操作执行完成', status: 'wait' }
    ],
    deactivate: [
      { title: '准备停用', description: '验证选中项目状态', status: 'wait' },
      { title: '执行停用', description: '批量停用规则明细', status: 'wait' },
      { title: '更新缓存', description: '刷新相关缓存数据', status: 'wait' },
      { title: '完成操作', description: '操作执行完成', status: 'wait' }
    ],
    delete: [
      { title: '准备删除', description: '验证删除权限和依赖', status: 'wait' },
      { title: '执行删除', description: '批量删除规则明细', status: 'wait' },
      { title: '清理数据', description: '清理相关数据和缓存', status: 'wait' },
      { title: '完成操作', description: '操作执行完成', status: 'wait' }
    ]
  }

  operationSteps.value = stepTemplates[props.operationType] || []
  currentStep.value = 0
}

const updateOperationStep = (stepIndex, status = 'process') => {
  if (operationSteps.value[stepIndex]) {
    operationSteps.value[stepIndex].status = status
    currentStep.value = stepIndex
  }
}

// 进度监控方法
const handlePauseOperation = () => {
  operationStatus.value = 'paused'
  canPauseOperation.value = false
  canResumeOperation.value = true
}

const handleResumeOperation = () => {
  operationStatus.value = 'processing'
  canPauseOperation.value = true
  canResumeOperation.value = false
}

const handleCancelOperation = () => {
  operationStatus.value = 'cancelled'
  isProcessing.value = false
  canPauseOperation.value = false
  canResumeOperation.value = false
  canCancelOperation.value = false
}

const handleRetryOperation = () => {
  // 重置失败的项目，重新开始操作
  const failedItems = operationErrors.value.map(error => error.data)
  if (failedItems.length > 0) {
    // 这里可以实现重试逻辑
    console.log('重试失败项目:', failedItems)
  }
}

const handleExportResult = () => {
  // 导出操作结果
  const result = {
    operation: props.operationType,
    total: totalItems.value,
    success: successCount.value,
    failed: errorCount.value,
    errors: operationErrors.value,
    timestamp: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `batch_operation_result_${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// 方法定义
const toggleShowAll = () => {
  showAllItems.value = !showAllItems.value
}

const handleConfirm = async () => {
  try {
    // 初始化操作状态
    isProcessing.value = true
    operationStatus.value = 'processing'
    operationStartTime.value = new Date()
    progressPercentage.value = 0
    progressStatus.value = 'active'
    operationResult.value = null

    // 重置计数器
    processedCount.value = 0
    successCount.value = 0
    errorCount.value = 0
    operationErrors.value = []

    // 初始化操作步骤
    initializeOperationSteps()

    // 启用操作控制
    canPauseOperation.value = true
    canResumeOperation.value = false
    canCancelOperation.value = true
    canRetryOperation.value = false

    const total = props.selectedDetails.length
    let success = 0
    let failed = 0
    const errors = []

    // 准备操作数据
    const operations = props.selectedDetails.map(detail => ({
      id: detail.id,
      rule_detail_id: detail.rule_detail_id,
      operation: props.operationType
    }))

    // 第一步：准备操作
    updateOperationStep(0, 'process')
    progressText.value = '正在准备操作...'
    await new Promise(resolve => setTimeout(resolve, 500))
    updateOperationStep(0, 'finish')

    // 第二步：执行操作
    updateOperationStep(1, 'process')

    if (props.operationType === 'delete') {
      // 批量删除
      progressText.value = '正在执行批量删除...'
      try {
        const detailIds = props.selectedDetails.map(detail => detail.id)
        await batchDeleteRuleDetails(props.ruleKey, detailIds)
        success = total
        successCount.value = total
        processedCount.value = total
        progressPercentage.value = 100
        progressStatus.value = 'success'
        progressText.value = '删除完成'
      } catch (error) {
        failed = total
        errorCount.value = total
        processedCount.value = total
        const errorInfo = {
          index: 0,
          id: 'batch',
          name: '批量删除',
          message: error.message,
          data: props.selectedDetails
        }
        errors.push(errorInfo)
        operationErrors.value.push(errorInfo)
        progressPercentage.value = 100
        progressStatus.value = 'exception'
        progressText.value = '删除失败'
      }
    } else {
      // 批量激活/停用
      const newStatus = props.operationType === 'activate' ? 'ACTIVE' : 'INACTIVE'

      for (let i = 0; i < operations.length; i++) {
        // 检查取消状态
        if (operationStatus.value === 'cancelled') break

        // 暂停支持
        while (operationStatus.value === 'paused') {
          await new Promise(resolve => setTimeout(resolve, 100))
        }

        const operation = operations[i]
        const currentItem = props.selectedDetails[i]
        progressText.value = `正在处理第 ${i + 1} 项: ${currentItem.rule_name}`

        try {
          await batchUpdateRuleDetails(props.ruleKey, [{
            id: operation.id,
            status: newStatus
          }])
          success++
          successCount.value++
        } catch (error) {
          failed++
          errorCount.value++
          const errorInfo = {
            index: i,
            id: operation.id,
            name: currentItem.rule_name,
            message: error.message,
            data: currentItem
          }
          errors.push(errorInfo)
          operationErrors.value.push(errorInfo)
        }

        processedCount.value = i + 1
        progressPercentage.value = Math.round(((i + 1) / total) * 100)

        // 模拟处理时间
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      progressStatus.value = failed === 0 ? 'success' : (success === 0 ? 'exception' : 'warning')
      progressText.value = '操作完成'
    }

    updateOperationStep(1, 'finish')

    // 第三步：更新缓存
    updateOperationStep(2, 'process')
    progressText.value = '正在更新缓存...'
    await new Promise(resolve => setTimeout(resolve, 300))
    updateOperationStep(2, 'finish')

    // 第四步：完成操作
    updateOperationStep(3, 'process')
    progressText.value = '操作执行完成'

    // 设置最终状态
    if (operationStatus.value !== 'cancelled') {
      operationStatus.value = failed === 0 ? 'completed' : 'error'
    }

    // 更新操作控制状态
    canPauseOperation.value = false
    canResumeOperation.value = false
    canCancelOperation.value = false
    canRetryOperation.value = failed > 0

    updateOperationStep(3, 'finish')

    // 设置操作结果
    operationResult.value = {
      title: failed === 0 ? '操作成功' : (success === 0 ? '操作失败' : '部分成功'),
      type: failed === 0 ? 'success' : (success === 0 ? 'error' : 'warning'),
      success,
      failed,
      errors
    }

    // 如果全部成功，延迟关闭对话框
    if (failed === 0 && operationStatus.value === 'completed') {
      setTimeout(() => {
        emit('success')
      }, 1500)
    }

  } catch (error) {
    console.error('批量操作失败:', error)
    operationResult.value = {
      title: '操作失败',
      type: 'error',
      success: 0,
      failed: props.selectedDetails.length,
      errors: [{ id: 'system', name: '系统错误', message: error.message }]
    }
    progressPercentage.value = 100
    progressStatus.value = 'exception'
    progressText.value = '操作失败'
  } finally {
    isProcessing.value = false
  }
}

const handleCancel = () => {
  if (operationResult.value) {
    emit('success') // 有结果时，关闭也算成功（需要刷新列表）
  } else {
    emit('cancel')
  }
}

// 重置状态
const resetState = () => {
  showAllItems.value = false
  updateRelated.value = false
  sendNotification.value = true
  deleteConfirmText.value = ''
  isProcessing.value = false
  progressPercentage.value = 0
  progressStatus.value = ''
  progressText.value = ''
  operationResult.value = null
}

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    resetState()
  }
})
</script>

<style scoped>
.batch-operation-content {
  max-height: 60vh;
  overflow-y: auto;
}

.operation-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #fdf6ec;
  border: 1px solid #fcdcb8;
  border-radius: 8px;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.warning-icon {
  color: #e6a23c;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: #e6a23c;
}

.info-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.selected-items {
  margin-bottom: 20px;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.items-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.items-list {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.items-list.collapsed {
  max-height: 160px;
}

.item-row {
  padding: 8px 12px;
  border-bottom: 1px solid #f5f7fa;
}

.item-row:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.item-id {
  font-family: monospace;
  color: #909399;
  font-size: 12px;
  min-width: 80px;
}

.item-name {
  flex: 1;
  color: #303133;
  font-size: 14px;
}

.more-items {
  padding: 8px 12px;
  text-align: center;
  color: #909399;
  font-size: 12px;
  background: #f8f9fa;
}

.operation-options {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.options-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.delete-confirmation {
  margin-bottom: 20px;
}

.confirmation-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.confirmation-input {
  margin-top: 16px;
}

.input-label {
  font-size: 14px;
  color: #606266;
}

.progress-section,
.result-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.progress-title,
.result-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
}

.result-details {
  margin-top: 8px;
}

.error-list {
  margin-top: 8px;
}

.error-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.error-list ul {
  margin: 0;
  padding-left: 20px;
}

.error-list li {
  margin-bottom: 4px;
  font-size: 13px;
}

.dialog-footer {
  text-align: right;
}
</style>
