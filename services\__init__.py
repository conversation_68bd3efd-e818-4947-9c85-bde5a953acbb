"""
服务层模块

该模块包含系统的所有业务逻辑服务，按功能分类组织：

1. 规则相关服务：
   - rule_service: 核心规则服务
   - rule_loader: 规则加载服务
   - rule_query_service: 规则查询服务
   - rule_registration_service: 规则注册服务
   - rule_change_detector: 规则变更检测

2. 规则明细服务 (rule_details/):
   - 数据迁移服务
   - 数据验证服务
   - 回滚服务

3. 数据管理服务 (data_management/):
   - 数据映射引擎
   - 数据一致性检查
   - 差异分析

4. 任务管理服务 (task_management/):
   - 任务状态管理
   - 任务恢复
   - 批处理管理

5. 系统服务：
   - monitoring_service: 监控服务
   - validation_service: 验证服务
   - sync_service: 同步服务
   - template_generator: 模板生成器
"""

# 系统服务（按字母顺序）
from .alert_manager import AlertManager
from .compensation_transaction_service import CompensationTransactionService

# 分组服务模块（按字母顺序）
from .data_management import (
    DataConsistencyChecker,
    DataMappingEngine,
    DifferenceAnalyzer,
)
from .degradation_sync import DegradationSyncService

# Import migration data validator (fixed)
from .migration_data_validator import MigrationDataValidator
from .monitoring_service import monitoring_service

# 核心规则服务（按字母顺序）
from .rule_change_detector import RuleChangeDetector
from .rule_detail_service import RuleDetailService

# Still disabled - need more work
# from .rule_details import (
#    EnhancedRuleDataMigration,
#    MigrationRollbackService,
#    RuleDataMigration,
# )
from .rule_loader import load_rules_from_db, load_rules_into_cache

from .rule_query_service import RuleQueryService
from .rule_registration_service import RuleRegistrationService
from .rule_service import RuleService
from .service_degradation_manager import ServiceDegradationManager
from .sync_service import RuleSyncService
from .task_management import (
    AdaptiveBatchSizeManager,
    IntelligentBatchProcessor,
    PersistentTaskStatusManager,
    TaskRecoveryService,
    TaskStatus,
    get_task_status_manager,
)
from .template_generator import TemplateGenerator
from .validation_service import ValidationService

__all__ = [
    # 系统服务
    "AlertManager",
    "CompensationTransactionService",
    "DegradationSyncService",
    "monitoring_service",
    "ServiceDegradationManager",
    "RuleSyncService",
    "TemplateGenerator",
    "ValidationService",
    # 核心规则服务
    "RuleChangeDetector",
    "RuleDetailService",
    "load_rules_from_db",
    "load_rules_into_cache",
    "RuleQueryService",
    "RuleRegistrationService",
    "RuleService",
    # 数据管理服务
    "DataConsistencyChecker",
    "DataMappingEngine",
    "DifferenceAnalyzer",
    # 规则明细服务 (partially restored)
    # "EnhancedRuleDataMigration",
    "MigrationDataValidator",
    # "MigrationRollbackService",
    # "RuleDataMigration",
    # 任务管理服务
    "AdaptiveBatchSizeManager",
    "IntelligentBatchProcessor",
    "PersistentTaskStatusManager",
    "TaskRecoveryService",
    "TaskStatus",
    "get_task_status_manager",
]
