<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    direction="rtl"
    size="60%"
    :before-close="handleClose"
  >
    <LoadingWrapper :loading="detailLoading" min-height="300px">
      <div v-if="currentRule" class="rule-detail-content">
        <!-- 规则基本信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <StatusTag :status="currentRule.status" />
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="规则名称">
              {{ currentRule.rule_name }}
            </el-descriptions-item>
            <el-descriptions-item label="规则Key">
              <el-tag type="info" size="small">{{ currentRule.rule_key }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="规则状态">
              <StatusTag :status="currentRule.status" />
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDate(currentRule.updated_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="规则类型" :span="2">
              {{ currentRule.rule_type || '通用规则' }}
            </el-descriptions-item>
            <el-descriptions-item label="规则描述" :span="2">
              {{ currentRule.description || '暂无描述' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 规则参数Schema -->
        <el-card v-if="ruleSchema.length > 0" class="schema-card" shadow="never">
          <template #header>
            <span>参数定义</span>
          </template>

          <el-table :data="ruleSchema || []" border>
            <el-table-column prop="name_cn" label="参数名称" width="150" />
            <el-table-column prop="name_en" label="英文名称" width="150" />
            <el-table-column prop="type" label="数据类型" width="120">
              <template #default="{ row }">
                <el-tag size="small" :type="getTypeTagType(row.type)">
                  {{ formatType(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="required" label="是否必填" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.required ? 'danger' : 'info'" size="small">
                  {{ row.required ? '必填' : '可选' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="说明" min-width="200">
              <template #default="{ row }">
                {{ getParameterDescription(row) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 统计信息 -->
        <el-card v-if="ruleStatistics && Object.keys(ruleStatistics).length > 0" class="stats-card" shadow="never">
          <template #header>
            <span>统计信息</span>
          </template>

          <el-row :gutter="16">
            <el-col :span="8" v-for="(value, key) in (ruleStatistics || {})" :key="key">
              <div class="stat-item">
                <div class="stat-value">{{ value }}</div>
                <div class="stat-label">{{ formatStatLabel(key) }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button-group>
            <el-button :icon="Download" @click="handleDownloadTemplate">
              下载模板
            </el-button>
            <el-button type="primary" :icon="Upload" @click="handleUploadData">
              上传数据
            </el-button>
          </el-button-group>
        </div>
      </div>
    </LoadingWrapper>
  </el-drawer>
</template>

<script setup>
import { Download, Upload } from '@element-plus/icons-vue'
import { formatDate } from '../../utils/dateUtils'
import LoadingWrapper from '../common/LoadingWrapper.vue'
import StatusTag from '../common/StatusTag.vue'
import { useRuleDetailDrawer } from '../../composables/business/useRuleDetail'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ruleKey: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'download-template', 'upload-data'])

// 使用规则详情抽屉功能
const {
  visible,
  currentRule,
  ruleSchema,
  ruleStatistics,
  detailLoading,
  drawerTitle,
  ruleSchemaFormatted,
  ruleStatsFormatted,
  handleClose,
  handleDownloadTemplate,
  handleUploadData,
  getTypeTagType
} = useRuleDetailDrawer(props, emit)

// 兼容性方法 - 保持模板中的函数调用正常工作
const formatType = (type) => {
  const typeMap = {
    'str': '字符串',
    'int': '整数',
    'float': '浮点数',
    'bool': '布尔值',
    'list[str]': '字符串列表'
  }
  return typeMap[type] || type
}

const getParameterDescription = (param) => {
  if (!param) return '暂无说明'
  const descriptions = {
    'rule_name': '规则的唯一名称标识',
    'drug_names': '药品名称列表，支持多个药品',
    'max_days': '最大支付天数限制',
    'gender': '适用性别限制'
  }
  return descriptions[param.name_en] || '暂无说明'
}

const formatStatLabel = (key) => {
  const labelMap = {
    'total_count': '总数量',
    'active_count': '活跃数量',
    'inactive_count': '非活跃数量',
    'error_count': '错误数量',
    'success_rate': '成功率'
  }
  return labelMap[key] || key
}
</script>

<style scoped>
.rule-detail-content {
  padding: 0 16px 16px 0;
}

.info-card,
.schema-card,
.stats-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.action-buttons {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-drawer__header) {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}
</style>
