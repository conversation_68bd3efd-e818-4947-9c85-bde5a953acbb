<template>
  <div class="incremental-data-comparison">
    <!-- 操作统计卡片 -->
    <el-row :gutter="16" class="summary-cards">
      <el-col :span="6">
        <el-card class="summary-card create-card">
          <div class="summary-content">
            <el-icon class="summary-icon"><Plus /></el-icon>
            <div class="summary-text">
              <div class="summary-number">{{ operationSummary.create }}</div>
              <div class="summary-label">新增</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card update-card">
          <div class="summary-content">
            <el-icon class="summary-icon"><Edit /></el-icon>
            <div class="summary-text">
              <div class="summary-number">{{ operationSummary.update }}</div>
              <div class="summary-label">更新</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card delete-card">
          <div class="summary-content">
            <el-icon class="summary-icon"><Delete /></el-icon>
            <div class="summary-text">
              <div class="summary-number">{{ operationSummary.delete }}</div>
              <div class="summary-label">删除</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card total-card">
          <div class="summary-content">
            <el-icon class="summary-icon"><DataAnalysis /></el-icon>
            <div class="summary-text">
              <div class="summary-number">{{ operationSummary.total }}</div>
              <div class="summary-label">总计</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户确认选项 -->
    <el-card class="confirmation-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>操作确认</span>
        </div>
      </template>
      <el-row :gutter="16">
        <el-col :span="8">
          <el-checkbox 
            v-model="confirmations.create" 
            :disabled="operationSummary.create === 0"
            size="large"
          >
            执行新增操作 ({{ operationSummary.create }}条)
          </el-checkbox>
        </el-col>
        <el-col :span="8">
          <el-checkbox 
            v-model="confirmations.update" 
            :disabled="operationSummary.update === 0"
            size="large"
          >
            执行更新操作 ({{ operationSummary.update }}条)
          </el-checkbox>
        </el-col>
        <el-col :span="8">
          <el-checkbox 
            v-model="confirmations.delete" 
            :disabled="operationSummary.delete === 0"
            size="large"
          >
            执行删除操作 ({{ operationSummary.delete }}条)
          </el-checkbox>
        </el-col>
      </el-row>
      <div class="confirmation-note">
        <el-alert
          title="注意：删除操作将永久删除数据库中存在但Excel文件中不存在的记录，请谨慎选择。"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>
    </el-card>

    <!-- 数据对比详情 -->
    <el-card class="comparison-details">
      <template #header>
        <div class="card-header">
          <span>数据对比详情</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 新增数据Tab -->
        <el-tab-pane 
          :label="`新增数据 (${comparisonResult.create.length})`" 
          name="create"
          :disabled="comparisonResult.create.length === 0"
        >
          <div v-if="comparisonResult.create.length > 0" class="operation-list">
            <div 
              v-for="(item, index) in paginatedCreateData" 
              :key="`create-${index}`" 
              class="operation-item create-item"
            >
              <div class="operation-header">
                <el-tag type="success" size="small">新增</el-tag>
                <span class="rule-name">{{ item.data.rule_name }}</span>
                <span class="rule-id">ID: {{ item.data.rule_id }}</span>
              </div>
              <div class="operation-content">
                <el-descriptions :column="3" size="small" border>
                  <el-descriptions-item 
                    v-for="field in displayFields" 
                    :key="field.key"
                    :label="field.label"
                  >
                    <span class="field-value">{{ item.data[field.key] || '-' }}</span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
            
            <!-- 分页 -->
            <el-pagination
              v-if="comparisonResult.create.length > pageSize"
              v-model:current-page="createCurrentPage"
              :page-size="pageSize"
              :total="comparisonResult.create.length"
              layout="prev, pager, next, total"
              class="pagination"
            />
          </div>
          <el-empty v-else description="没有新增数据" />
        </el-tab-pane>

        <!-- 更新数据Tab -->
        <el-tab-pane 
          :label="`更新数据 (${comparisonResult.update.length})`" 
          name="update"
          :disabled="comparisonResult.update.length === 0"
        >
          <div v-if="comparisonResult.update.length > 0" class="operation-list">
            <div 
              v-for="(item, index) in paginatedUpdateData" 
              :key="`update-${index}`" 
              class="operation-item update-item"
            >
              <div class="operation-header">
                <el-tag type="warning" size="small">更新</el-tag>
                <span class="rule-name">{{ item.data.rule_name }}</span>
                <span class="rule-id">ID: {{ item.data.rule_id }}</span>
                <el-tag size="small" type="info">{{ item.changes.length }}个字段变更</el-tag>
              </div>
              <div class="operation-content">
                <!-- 变更字段对比 -->
                <div class="changes-section">
                  <h4>变更字段:</h4>
                  <div class="changes-list">
                    <div 
                      v-for="change in item.changes" 
                      :key="change.field" 
                      class="change-item"
                    >
                      <div class="change-field">{{ change.fieldLabel }}</div>
                      <div class="change-values">
                        <div class="old-value">
                          <span class="value-label">原值:</span>
                          <span class="value-content">{{ change.oldValue || '-' }}</span>
                        </div>
                        <el-icon class="arrow-icon"><Right /></el-icon>
                        <div class="new-value">
                          <span class="value-label">新值:</span>
                          <span class="value-content">{{ change.newValue || '-' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 分页 -->
            <el-pagination
              v-if="comparisonResult.update.length > pageSize"
              v-model:current-page="updateCurrentPage"
              :page-size="pageSize"
              :total="comparisonResult.update.length"
              layout="prev, pager, next, total"
              class="pagination"
            />
          </div>
          <el-empty v-else description="没有更新数据" />
        </el-tab-pane>

        <!-- 删除数据Tab -->
        <el-tab-pane 
          :label="`删除数据 (${comparisonResult.delete.length})`" 
          name="delete"
          :disabled="comparisonResult.delete.length === 0"
        >
          <div v-if="comparisonResult.delete.length > 0" class="operation-list">
            <div 
              v-for="(item, index) in paginatedDeleteData" 
              :key="`delete-${index}`" 
              class="operation-item delete-item"
            >
              <div class="operation-header">
                <el-tag type="danger" size="small">删除</el-tag>
                <span class="rule-name">{{ item.data.rule_name }}</span>
                <span class="rule-id">ID: {{ item.data.rule_id }}</span>
              </div>
              <div class="operation-content">
                <el-descriptions :column="3" size="small" border>
                  <el-descriptions-item 
                    v-for="field in displayFields" 
                    :key="field.key"
                    :label="field.label"
                  >
                    <span class="field-value">{{ item.data[field.key] || '-' }}</span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
            
            <!-- 分页 -->
            <el-pagination
              v-if="comparisonResult.delete.length > pageSize"
              v-model:current-page="deleteCurrentPage"
              :page-size="pageSize"
              :total="comparisonResult.delete.length"
              layout="prev, pager, next, total"
              class="pagination"
            />
          </div>
          <el-empty v-else description="没有删除数据" />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm"
        :disabled="!hasConfirmedOperations"
        :loading="isUploading"
      >
        确认执行 ({{ confirmedOperationsCount }}项操作)
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Plus, Edit, Delete, DataAnalysis, Right } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  comparisonResult: {
    type: Object,
    required: true,
    default: () => ({
      create: [],
      update: [],
      delete: []
    })
  },
  operationSummary: {
    type: Object,
    required: true,
    default: () => ({
      create: 0,
      update: 0,
      delete: 0,
      total: 0
    })
  },
  isUploading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['confirm', 'cancel'])

// 响应式数据
const activeTab = ref('create')
const pageSize = 5

// 分页状态
const createCurrentPage = ref(1)
const updateCurrentPage = ref(1)
const deleteCurrentPage = ref(1)

// 用户确认选项
const confirmations = ref({
  create: true,
  update: true,
  delete: false // 默认不删除
})

// 显示字段配置
const displayFields = [
  { key: 'rule_name', label: '规则名称' },
  { key: 'error_level_1', label: '错误级别1' },
  { key: 'error_level_2', label: '错误级别2' },
  { key: 'status', label: '状态' },
  { key: 'description', label: '描述' },
  { key: 'rule_type', label: '规则类型' }
]

// 计算属性
const paginatedCreateData = computed(() => {
  const start = (createCurrentPage.value - 1) * pageSize
  const end = start + pageSize
  return props.comparisonResult.create.slice(start, end)
})

const paginatedUpdateData = computed(() => {
  const start = (updateCurrentPage.value - 1) * pageSize
  const end = start + pageSize
  return props.comparisonResult.update.slice(start, end)
})

const paginatedDeleteData = computed(() => {
  const start = (deleteCurrentPage.value - 1) * pageSize
  const end = start + pageSize
  return props.comparisonResult.delete.slice(start, end)
})

const hasConfirmedOperations = computed(() => {
  return (confirmations.value.create && props.operationSummary.create > 0) ||
         (confirmations.value.update && props.operationSummary.update > 0) ||
         (confirmations.value.delete && props.operationSummary.delete > 0)
})

const confirmedOperationsCount = computed(() => {
  let count = 0
  if (confirmations.value.create) count += props.operationSummary.create
  if (confirmations.value.update) count += props.operationSummary.update
  if (confirmations.value.delete) count += props.operationSummary.delete
  return count
})

// 方法
const handleConfirm = () => {
  emit('confirm', confirmations.value)
}

// 监听数据变化，自动切换到有数据的Tab
watch(() => props.comparisonResult, (newResult) => {
  if (newResult.create.length > 0) {
    activeTab.value = 'create'
  } else if (newResult.update.length > 0) {
    activeTab.value = 'update'
  } else if (newResult.delete.length > 0) {
    activeTab.value = 'delete'
  }
}, { immediate: true })
</script>

<style scoped>
.incremental-data-comparison {
  padding: 20px;
}

/* 统计卡片样式 */
.summary-cards {
  margin-bottom: 20px;
}

.summary-card {
  text-align: center;
  cursor: default;
}

.summary-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.summary-icon {
  font-size: 24px;
}

.summary-number {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.summary-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.create-card .summary-icon,
.create-card .summary-number {
  color: #67c23a;
}

.update-card .summary-icon,
.update-card .summary-number {
  color: #e6a23c;
}

.delete-card .summary-icon,
.delete-card .summary-number {
  color: #f56c6c;
}

.total-card .summary-icon,
.total-card .summary-number {
  color: #409eff;
}

/* 确认选项样式 */
.confirmation-card {
  margin-bottom: 20px;
}

.confirmation-note {
  margin-top: 16px;
}

/* 对比详情样式 */
.comparison-details {
  margin-bottom: 20px;
}

.operation-list {
  max-height: 600px;
  overflow-y: auto;
}

.operation-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 16px;
  overflow: hidden;
}

.operation-header {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  gap: 12px;
}

.rule-name {
  font-weight: bold;
  color: #303133;
}

.rule-id {
  color: #909399;
  font-size: 12px;
}

.operation-content {
  padding: 16px;
}

/* 变更字段样式 */
.changes-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.changes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.change-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.change-field {
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.change-values {
  display: flex;
  align-items: center;
  gap: 12px;
}

.old-value,
.new-value {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.value-label {
  font-size: 12px;
  color: #909399;
}

.value-content {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  min-height: 20px;
  word-break: break-all;
}

.old-value .value-content {
  background-color: #fef0f0;
  border-color: #fbc4c4;
}

.new-value .value-content {
  background-color: #f0f9ff;
  border-color: #b3d8ff;
}

.arrow-icon {
  color: #909399;
  font-size: 16px;
}

/* 操作项特殊样式 */
.create-item {
  border-left: 4px solid #67c23a;
}

.update-item {
  border-left: 4px solid #e6a23c;
}

.delete-item {
  border-left: 4px solid #f56c6c;
}

/* 分页样式 */
.pagination {
  margin-top: 20px;
  text-align: center;
}

/* 操作按钮样式 */
.action-buttons {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.action-buttons .el-button {
  margin: 0 8px;
  min-width: 120px;
}

/* 字段值样式 */
.field-value {
  word-break: break-all;
  max-width: 200px;
  display: inline-block;
}
</style>
