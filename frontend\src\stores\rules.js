import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import {
  getRulesStatus,
  getRuleDetail,
  getRuleSchema,
  getRuleStatistics,
  downloadRuleTemplate,
  confirmRuleSubmission
} from '../api/rules'
import { ElMessage } from 'element-plus'

/**
 * 规则管理Store
 * 使用Composition API语法，提供规则相关的状态管理和操作方法
 */
export const useRulesStore = defineStore('rules', () => {
  // ==================== 状态定义 ====================

  // 规则列表相关状态
  const rules = ref([])
  const loading = ref(false)
  const lastFetchTime = ref(null)

  // 规则详情相关状态
  const currentRule = ref(null)
  const ruleSchema = ref([])
  const ruleStatistics = ref({})
  const detailLoading = ref(false)

  // 缓存和性能优化状态
  const rulesCache = ref(new Map())
  const schemaCache = ref(new Map())
  const statsCache = ref(new Map())

  // 规则明细相关状态（新增）
  const currentRuleDetails = ref([])      // 当前规则的明细列表
  const ruleDetailsCache = ref(new Map()) // 明细缓存
  const detailsLoading = ref(false)       // 明细加载状态
  const detailsCount = ref(0)             // 明细总数

  // ==================== 计算属性 ====================

  // 基础统计
  const rulesCount = computed(() => rules.value?.length || 0)

  const hasRules = computed(() => (rules.value?.length || 0) > 0)

  const isLoading = computed(() => loading.value || detailLoading.value)

  // 按状态分组的规则
  const rulesByStatus = computed(() => {
    const statusGroups = {}
    if (rules.value && Array.isArray(rules.value)) {
      rules.value.forEach(rule => {
        const status = rule?.status || 'UNKNOWN'
        if (!statusGroups[status]) {
          statusGroups[status] = []
        }
        statusGroups[status].push(rule)
      })
    }
    return statusGroups
  })

  // 状态统计
  const statusCounts = computed(() => {
    const counts = {}
    if (rules.value && Array.isArray(rules.value)) {
      rules.value.forEach(rule => {
        const status = rule?.status || 'UNKNOWN'
        counts[status] = (counts[status] || 0) + 1
      })
    }
    return counts
  })

  // 可用状态列表
  const availableStatuses = computed(() => {
    return Object.keys(statusCounts.value).sort()
  })

  // 规则状态摘要
  const rulesSummary = computed(() => ({
    total: rulesCount.value,
    ready: statusCounts.value.READY || 0,
    new: statusCounts.value.NEW || 0,
    changed: statusCounts.value.CHANGED || 0,
    deprecated: statusCounts.value.DEPRECATED || 0,
    lastUpdate: lastFetchTime.value
  }))

  // 当前规则是否有效
  const hasCurrentRule = computed(() => {
    return currentRule.value?.rule_key
  })

  // 规则明细相关计算属性（新增）
  const hasRuleDetails = computed(() => (currentRuleDetails.value?.length || 0) > 0)
  const ruleDetailsCount = computed(() => currentRuleDetails.value?.length || 0)
  const isDetailsLoading = computed(() => detailsLoading.value)

  // ==================== 操作方法 ====================

  /**
   * 获取规则列表
   * @param {boolean} forceRefresh - 是否强制刷新，忽略缓存
   * @returns {Promise<Array>} 规则列表
   */
  const fetchRules = async (forceRefresh = false) => {
    // 如果不是强制刷新且有缓存数据，直接返回
    if (!forceRefresh && (rules.value?.length || 0) > 0 && lastFetchTime.value) {
      const timeDiff = Date.now() - lastFetchTime.value
      // 5分钟内的数据认为是新鲜的
      if (timeDiff < 5 * 60 * 1000) {
        return rules.value
      }
    }

    loading.value = true
    try {
      const response = await getRulesStatus()

      // 处理统一API响应格式
      let rulesData = []
      if (response && typeof response === 'object') {
        if (response.success && response.data) {
          // 新的统一格式：{success: true, data: [...]}
          rulesData = response.data
        } else if (Array.isArray(response)) {
          // 旧格式：直接是数组
          rulesData = response
        } else {
          console.warn('未知的API响应格式:', response)
          rulesData = []
        }
      }

      rules.value = rulesData || []
      lastFetchTime.value = Date.now()

      // 更新缓存
      if (rules.value && Array.isArray(rules.value)) {
        rules.value.forEach(rule => {
          if (rule?.rule_key) {
            rulesCache.value.set(rule.rule_key, rule)
          }
        })
      }

      console.log('规则列表获取成功:', {
        count: rules.value.length,
        format: response?.success ? 'unified' : 'legacy'
      })

      return rules.value
    } catch (error) {
      console.error('获取规则列表失败:', error)
      ElMessage.error('获取规则列表失败')

      // 如果有缓存数据，使用缓存
      if ((rules.value?.length || 0) === 0 && rulesCache.value.size > 0) {
        rules.value = Array.from(rulesCache.value.values())
        ElMessage.warning('网络异常，显示缓存数据')
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取规则详情
   * @param {string} ruleKey - 规则键
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 规则详情对象
   */
  const fetchRuleDetail = async (ruleKey, useCache = true) => {
    if (!ruleKey) return null

    // 检查缓存
    if (useCache) {
      const cachedRule = rulesCache.value.get(ruleKey)
      const cachedSchema = schemaCache.value.get(ruleKey)
      const cachedStats = statsCache.value.get(ruleKey)

      if (cachedRule && cachedSchema && cachedStats) {
        currentRule.value = cachedRule
        ruleSchema.value = cachedSchema
        ruleStatistics.value = cachedStats
        return { detail: cachedRule, schema: cachedSchema, statistics: cachedStats }
      }
    }

    detailLoading.value = true
    try {
      const [detail, schema, statistics] = await Promise.allSettled([
        getRuleDetail(ruleKey),
        getRuleSchema(ruleKey),
        getRuleStatistics(ruleKey)
      ])

      // 处理规则详情
      if (detail.status === 'fulfilled') {
        currentRule.value = detail.value
        rulesCache.value.set(ruleKey, detail.value)
      } else {
        console.warn('获取规则详情失败:', detail.reason)
        currentRule.value = rules.value.find(r => r.rule_key === ruleKey) || null
      }

      // 处理规则Schema
      if (schema.status === 'fulfilled') {
        ruleSchema.value = schema.value || []
        schemaCache.value.set(ruleKey, schema.value || [])
      } else {
        console.warn('获取规则Schema失败:', schema.reason)
        ruleSchema.value = schemaCache.value.get(ruleKey) || []
      }

      // 处理统计信息
      if (statistics.status === 'fulfilled') {
        ruleStatistics.value = statistics.value || {}
        statsCache.value.set(ruleKey, statistics.value || {})
      } else {
        console.warn('获取规则统计失败:', statistics.reason)
        ruleStatistics.value = statsCache.value.get(ruleKey) || {}
      }

      return {
        detail: currentRule.value,
        schema: ruleSchema.value,
        statistics: ruleStatistics.value
      }
    } catch (error) {
      console.error('获取规则详情失败:', error)
      ElMessage.error('获取规则详情失败')
      throw error
    } finally {
      detailLoading.value = false
    }
  }

  /**
   * 下载规则模板
   * @param {Object} rule - 规则对象
   * @returns {Promise<void>}
   */
  const downloadTemplate = async (rule) => {
    try {
      await downloadRuleTemplate(rule.rule_key, rule.rule_name)
      ElMessage.success(`${rule.rule_name} 模板下载成功`)
    } catch (error) {
      console.error('下载模板失败:', error)
      ElMessage.error('下载模板失败')
      throw error
    }
  }

  /**
   * 提交规则数据
   * @param {string} ruleKey - 规则键
   * @param {Object} submissionData - 提交数据
   * @returns {Promise<Object>} 提交结果
   */
  const submitRuleData = async (ruleKey, submissionData) => {
    try {
      const result = await confirmRuleSubmission(ruleKey, submissionData)
      ElMessage.success('数据提交成功')

      // 更新本地规则状态和缓存
      await nextTick(() => {
        const ruleIndex = rules.value.findIndex(r => r.rule_key === ruleKey)
        if (ruleIndex !== -1) {
          const updatedRule = {
            ...rules.value[ruleIndex],
            status: 'READY',
            updated_at: new Date().toISOString()
          }
          rules.value[ruleIndex] = updatedRule
          rulesCache.value.set(ruleKey, updatedRule)
        }
      })

      return result
    } catch (error) {
      console.error('提交数据失败:', error)
      ElMessage.error('提交数据失败')
      throw error
    }
  }

  // ==================== 查询和筛选方法 ====================

  /**
   * 搜索规则（已废弃，建议使用useSearch Composable）
   * @deprecated 使用useSearch Composable替代
   */
  const searchRules = (keyword) => {
    if (!keyword) return rules.value

    const lowerKeyword = keyword.toLowerCase()
    return rules.value.filter(rule =>
      rule.rule_name?.toLowerCase().includes(lowerKeyword) ||
      rule.rule_key?.toLowerCase().includes(lowerKeyword)
    )
  }

  /**
   * 按状态筛选规则（已废弃，建议使用useSearch Composable）
   * @deprecated 使用useSearch Composable替代
   */
  const filterRulesByStatus = (status) => {
    if (!status || status === 'ALL') return rules.value
    return rules.value.filter(rule => rule.status === status)
  }

  /**
   * 根据规则键获取规则
   * @param {string} ruleKey - 规则键
   * @returns {Object|undefined} 规则对象
   */
  const getRuleByKey = (ruleKey) => {
    // 优先从缓存获取
    const cachedRule = rulesCache.value.get(ruleKey)
    if (cachedRule) return cachedRule

    return rules.value.find(rule => rule.rule_key === ruleKey)
  }

  // ==================== 规则明细基础方法（新增） ====================

  /**
   * 获取规则明细列表（基础版本）
   * @param {string} ruleKey - 规则键
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 明细列表
   */
  const fetchRuleDetailsList = async (ruleKey, params = {}) => {
    if (!ruleKey) return []

    const cacheKey = `details:${ruleKey}:${JSON.stringify(params)}`

    // 检查缓存
    if (ruleDetailsCache.value.has(cacheKey)) {
      const cached = ruleDetailsCache.value.get(cacheKey)
      if (Date.now() - cached.timestamp < 30 * 60 * 1000) { // 30分钟缓存
        currentRuleDetails.value = cached.data
        detailsCount.value = cached.data.length
        return cached.data
      }
    }

    detailsLoading.value = true
    try {
      // 这里可以调用 ruleDetailsAPI 或者使用现有的 API
      // 为了保持兼容性，先使用现有的 getRuleDetail 方法
      const ruleDetail = await getRuleDetail(ruleKey)
      const detailsList = ruleDetail?.data_set ? JSON.parse(ruleDetail.data_set) : []

      currentRuleDetails.value = detailsList
      detailsCount.value = detailsList.length

      // 更新缓存
      ruleDetailsCache.value.set(cacheKey, {
        data: detailsList,
        timestamp: Date.now()
      })

      return detailsList
    } catch (error) {
      console.error('获取规则明细列表失败:', error)
      ElMessage.error('获取规则明细列表失败')
      return []
    } finally {
      detailsLoading.value = false
    }
  }

  /**
   * 获取规则明细数量
   * @param {string} ruleKey - 规则键
   * @returns {Promise<number>} 明细数量
   */
  const getRuleDetailsCount = async (ruleKey) => {
    if (!ruleKey) return 0

    try {
      // 如果已经有缓存的明细列表，直接返回数量
      if (currentRuleDetails.value.length > 0) {
        return currentRuleDetails.value.length
      }

      // 否则获取明细列表并返回数量
      const detailsList = await fetchRuleDetailsList(ruleKey)
      return detailsList.length
    } catch (error) {
      console.error('获取规则明细数量失败:', error)
      return 0
    }
  }

  /**
   * 清除规则明细缓存
   * @param {string} ruleKey - 规则键（可选，不传则清除所有）
   */
  const clearRuleDetailsCache = (ruleKey) => {
    if (ruleKey) {
      // 清除指定规则的缓存
      for (const [key] of ruleDetailsCache.value) {
        if (key.includes(ruleKey)) {
          ruleDetailsCache.value.delete(key)
        }
      }
    } else {
      // 清除所有明细缓存
      ruleDetailsCache.value.clear()
    }
  }

  /**
   * 刷新规则明细
   * @param {string} ruleKey - 规则键
   * @returns {Promise<Array>} 明细列表
   */
  const refreshRuleDetails = async (ruleKey) => {
    if (!ruleKey) return []

    // 清除缓存
    clearRuleDetailsCache(ruleKey)

    // 重新获取
    return await fetchRuleDetailsList(ruleKey)
  }

  // ==================== 状态管理方法 ====================

  /**
   * 清除当前规则详情
   */
  const clearCurrentRule = () => {
    currentRule.value = null
    ruleSchema.value = []
    ruleStatistics.value = {}
  }

  /**
   * 刷新单个规则
   * @param {string} ruleKey - 规则键
   * @returns {Promise<void>}
   */
  const refreshRule = async (ruleKey) => {
    const ruleIndex = rules.value.findIndex(r => r.rule_key === ruleKey)
    if (ruleIndex === -1) return

    try {
      const updatedRule = await getRuleDetail(ruleKey)
      const newRule = { ...rules.value[ruleIndex], ...updatedRule }
      rules.value[ruleIndex] = newRule
      rulesCache.value.set(ruleKey, newRule)
    } catch (error) {
      console.error('刷新规则失败:', error)
    }
  }

  // ==================== 缓存管理方法 ====================

  /**
   * 清除所有缓存
   */
  const clearCache = () => {
    rulesCache.value.clear()
    schemaCache.value.clear()
    statsCache.value.clear()
  }

  /**
   * 清除指定规则的缓存
   * @param {string} ruleKey - 规则键
   */
  const clearRuleCache = (ruleKey) => {
    rulesCache.value.delete(ruleKey)
    schemaCache.value.delete(ruleKey)
    statsCache.value.delete(ruleKey)
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  const getCacheStats = () => ({
    rulesCount: rulesCache.value.size,
    schemaCount: schemaCache.value.size,
    statsCount: statsCache.value.size,
    totalSize: rulesCache.value.size + schemaCache.value.size + statsCache.value.size
  })

  // ==================== 重置和清理方法 ====================

  /**
   * 重置整个Store状态
   */
  const resetStore = () => {
    rules.value = []
    currentRule.value = null
    ruleSchema.value = []
    ruleStatistics.value = {}
    loading.value = false
    detailLoading.value = false
    lastFetchTime.value = null

    // 重置规则明细相关状态（新增）
    currentRuleDetails.value = []
    detailsCount.value = 0
    detailsLoading.value = false

    clearCache()
    clearRuleDetailsCache() // 清除明细缓存
  }

  // ==================== 返回公共接口 ====================

  return {
    // 基础状态
    rules,
    currentRule,
    ruleSchema,
    ruleStatistics,
    loading,
    detailLoading,
    lastFetchTime,

    // 规则明细状态（新增）
    currentRuleDetails,
    detailsCount,
    detailsLoading,

    // 计算属性
    rulesCount,
    hasRules,
    isLoading,
    rulesByStatus,
    statusCounts,
    availableStatuses,
    rulesSummary,
    hasCurrentRule,

    // 规则明细计算属性（新增）
    hasRuleDetails,
    ruleDetailsCount,
    isDetailsLoading,

    // 核心操作方法
    fetchRules,
    fetchRuleDetail,
    downloadTemplate,
    submitRuleData,

    // 规则明细基础方法（新增）
    fetchRuleDetailsList,
    getRuleDetailsCount,
    clearRuleDetailsCache,
    refreshRuleDetails,

    // 查询和筛选方法（已废弃，建议使用Composables）
    searchRules,
    filterRulesByStatus,
    getRuleByKey,

    // 状态管理方法
    clearCurrentRule,
    refreshRule,

    // 缓存管理方法
    clearCache,
    clearRuleCache,
    getCacheStats,

    // 重置和清理方法
    resetStore
  }
})
