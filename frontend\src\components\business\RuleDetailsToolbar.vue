<template>
  <div class="rule-details-toolbar">
    <el-card shadow="never" class="toolbar-card">
      <div class="toolbar-content">
        <!-- 左侧操作按钮 -->
        <div class="toolbar-left">
          <el-button 
            type="primary" 
            :icon="Plus" 
            @click="handleCreate"
          >
            新增明细
          </el-button>
          
          <!-- 批量操作区域 -->
          <div v-if="hasSelected" class="batch-operations">
            <!-- 主要批量操作 -->
            <el-button-group class="primary-batch-actions">
              <el-button
                type="success"
                size="small"
                :icon="Check"
                @click="handleBatchCommand('activate')"
                :disabled="!canActivate"
                :title="`激活 ${activatableCount} 项 (Ctrl+A)`"
              >
                激活 ({{ activatableCount }})
              </el-button>
              <el-button
                type="warning"
                size="small"
                :icon="Close"
                @click="handleBatchCommand('deactivate')"
                :disabled="!canDeactivate"
                :title="`停用 ${deactivatableCount} 项 (Ctrl+S)`"
              >
                停用 ({{ deactivatableCount }})
              </el-button>
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                @click="handleBatchCommand('delete')"
                :disabled="!canDelete"
                :title="`删除 ${deletableCount} 项 (Ctrl+Delete)`"
              >
                删除 ({{ deletableCount }})
              </el-button>
            </el-button-group>

            <!-- 更多批量操作 -->
            <el-dropdown @command="handleBatchCommand" trigger="click" class="more-actions">
              <el-button size="small" :icon="MoreFilled">
                更多
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="copy" :icon="CopyDocument">
                    批量复制
                  </el-dropdown-item>
                  <el-dropdown-item command="move" :icon="Rank">
                    批量移动
                  </el-dropdown-item>
                  <el-dropdown-item command="export-selected" :icon="Download" divided>
                    导出选中项
                  </el-dropdown-item>
                  <el-dropdown-item command="set-default" :icon="Star">
                    设为默认
                  </el-dropdown-item>
                  <el-dropdown-item command="clear-default" :icon="StarFilled">
                    取消默认
                  </el-dropdown-item>
                  <el-dropdown-item command="batch-edit" :icon="Edit" divided>
                    批量编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="assign-category" :icon="Collection">
                    分配分类
                  </el-dropdown-item>
                  <el-dropdown-item command="set-priority" :icon="Sort">
                    设置优先级
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 快捷操作 -->
            <el-button
              size="small"
              text
              :icon="RefreshLeft"
              @click="handleClearSelection"
              title="清空选择 (Ctrl+D)"
              class="clear-selection"
            >
              清空
            </el-button>
          </div>
        </div>

        <!-- 右侧工具按钮 -->
        <div class="toolbar-right">
          <el-button-group>
            <el-button :icon="Download" @click="handleExport">
              导出
            </el-button>
            <el-button :icon="Upload" @click="handleImport">
              导入
            </el-button>
          </el-button-group>

          <el-dropdown @command="handleToolCommand" trigger="click">
            <el-button :icon="Tools">
              工具
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="template" :icon="Document">
                  下载模板
                </el-dropdown-item>
                <el-dropdown-item command="validate" :icon="CircleCheck">
                  数据验证
                </el-dropdown-item>
                <el-dropdown-item command="statistics" :icon="DataAnalysis" divided>
                  统计分析
                </el-dropdown-item>
                <el-dropdown-item command="history" :icon="Clock">
                  操作历史
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 选择信息栏 -->
      <div v-if="hasSelected" class="selection-info">
        <div class="selection-content">
          <el-icon class="selection-icon"><InfoFilled /></el-icon>
          <span class="selection-text">
            已选择 {{ selectedCount }} 条明细
          </span>
          <el-button 
            type="text" 
            size="small" 
            @click="handleClearSelection"
            class="clear-selection"
          >
            清除选择
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import {
  Plus,
  Operation,
  Download,
  Upload,
  Tools,
  Document,
  CircleCheck,
  DataAnalysis,
  Clock,
  Check,
  Close,
  Delete,
  InfoFilled,
  ArrowDown,
  MoreFilled,
  CopyDocument,
  Rank,
  Star,
  StarFilled,
  Edit,
  Collection,
  Sort,
  RefreshLeft
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  ruleKey: {
    type: String,
    required: true
  },
  selectedCount: {
    type: Number,
    default: 0
  },
  hasSelected: {
    type: Boolean,
    default: false
  },
  // 选中的项目列表
  selectedItems: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'create',
  'batch-operation',
  'export',
  'import',
  'clear-selection'
])

// 计算属性 - 分析选中项目的状态
const selectedItemsAnalysis = computed(() => {
  const items = props.selectedItems || []
  return {
    total: items.length,
    active: items.filter(item => item.status === 'ACTIVE').length,
    inactive: items.filter(item => item.status === 'INACTIVE').length,
    deleted: items.filter(item => item.status === 'DELETED').length,
    defaultSelected: items.filter(item => item.default_selected).length
  }
})

const activatableCount = computed(() => {
  return props.selectedItems.filter(item =>
    item.status === 'INACTIVE' || item.status === 'DELETED'
  ).length
})

const deactivatableCount = computed(() => {
  return props.selectedItems.filter(item => item.status === 'ACTIVE').length
})

const deletableCount = computed(() => {
  return props.selectedItems.filter(item => item.status !== 'DELETED').length
})

const canActivate = computed(() => activatableCount.value > 0)
const canDeactivate = computed(() => deactivatableCount.value > 0)
const canDelete = computed(() => deletableCount.value > 0)

// 方法定义
const handleCreate = () => {
  emit('create')
}

const handleBatchCommand = (command) => {
  // 验证操作的有效性
  if (!validateBatchOperation(command)) {
    return
  }

  emit('batch-operation', command)
}

const validateBatchOperation = (command) => {
  const analysis = selectedItemsAnalysis.value

  switch (command) {
    case 'activate':
      if (activatableCount.value === 0) {
        ElMessage.warning('没有可激活的项目')
        return false
      }
      break
    case 'deactivate':
      if (deactivatableCount.value === 0) {
        ElMessage.warning('没有可停用的项目')
        return false
      }
      break
    case 'delete':
      if (deletableCount.value === 0) {
        ElMessage.warning('没有可删除的项目')
        return false
      }
      break
    case 'copy':
      if (analysis.total === 0) {
        ElMessage.warning('请先选择要复制的项目')
        return false
      }
      break
    case 'export-selected':
      if (analysis.total === 0) {
        ElMessage.warning('请先选择要导出的项目')
        return false
      }
      break
    case 'set-default':
      if (analysis.total === 0) {
        ElMessage.warning('请先选择要设为默认的项目')
        return false
      }
      break
    case 'clear-default':
      if (analysis.defaultSelected === 0) {
        ElMessage.warning('选中项目中没有默认项目')
        return false
      }
      break
  }

  return true
}

const handleExport = () => {
  emit('export')
}

const handleImport = () => {
  emit('import')
}

const handleClearSelection = () => {
  emit('clear-selection')
}

const handleToolCommand = (command) => {
  switch (command) {
    case 'template':
      handleDownloadTemplate()
      break
    case 'validate':
      handleDataValidation()
      break
    case 'statistics':
      handleStatistics()
      break
    case 'history':
      handleOperationHistory()
      break
    default:
      console.log('未知工具命令:', command)
  }
}

const handleDownloadTemplate = () => {
  ElMessage.info('下载模板功能开发中...')
}

const handleDataValidation = () => {
  ElMessage.info('数据验证功能开发中...')
}

const handleStatistics = () => {
  ElMessage.info('统计分析功能开发中...')
}

const handleOperationHistory = () => {
  ElMessage.info('操作历史功能开发中...')
}

// 快捷键处理
const handleKeydown = (event) => {
  // 检查是否在输入框中
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return
  }

  if (event.ctrlKey || event.metaKey) {
    switch (event.key.toLowerCase()) {
      case 'a':
        if (props.hasSelected && canActivate.value) {
          event.preventDefault()
          handleBatchCommand('activate')
        }
        break
      case 's':
        if (props.hasSelected && canDeactivate.value) {
          event.preventDefault()
          handleBatchCommand('deactivate')
        }
        break
      case 'd':
        if (props.hasSelected) {
          event.preventDefault()
          handleClearSelection()
        }
        break
      case 'e':
        if (props.hasSelected) {
          event.preventDefault()
          handleBatchCommand('export-selected')
        }
        break
    }
  } else if (event.key === 'Delete' && props.hasSelected && canDelete.value) {
    event.preventDefault()
    handleBatchCommand('delete')
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 获取操作提示文本
const getOperationTip = (command) => {
  const tips = {
    activate: `将激活 ${activatableCount.value} 个停用或已删除的项目`,
    deactivate: `将停用 ${deactivatableCount.value} 个激活的项目`,
    delete: `将删除 ${deletableCount.value} 个项目（不包括已删除的）`,
    copy: `将复制 ${props.selectedCount} 个项目`,
    'export-selected': `将导出 ${props.selectedCount} 个选中项目`,
    'set-default': `将设置 ${props.selectedCount} 个项目为默认`,
    'clear-default': `将取消 ${selectedItemsAnalysis.value.defaultSelected} 个默认项目`
  }
  return tips[command] || ''
}
</script>

<style scoped>
.rule-details-toolbar {
  margin-bottom: 20px;
}

.toolbar-card {
  border: none;
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.batch-operations {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-left: 8px;
}

.primary-batch-actions {
  display: flex;
  gap: 4px;
}

.more-actions {
  margin-left: 8px;
}

.clear-selection {
  margin-left: 8px;
  color: #6c757d !important;
}

.clear-selection:hover {
  color: #495057 !important;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.selection-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.selection-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-size: 14px;
}

.selection-icon {
  font-size: 16px;
}

.selection-text {
  font-weight: 500;
}

.clear-selection {
  margin-left: auto;
  color: #909399;
}

.clear-selection:hover {
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .batch-operations {
    flex-direction: column;
    gap: 8px;
    margin-left: 0;
  }

  .primary-batch-actions {
    width: 100%;
  }

  .primary-batch-actions .el-button {
    flex: 1;
    font-size: 12px;
  }

  .more-actions,
  .clear-selection {
    margin-left: 0;
    align-self: center;
  }
}

@media (max-width: 480px) {
  .primary-batch-actions {
    flex-direction: column;
    gap: 4px;
  }

  .selection-content {
    flex-direction: column;
    text-align: center;
    gap: 4px;
  }
}
</style>
