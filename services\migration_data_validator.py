"""
数据迁移验证工具
负责验证迁移前后数据的完整性和一致性
"""

from datetime import datetime
from typing import Any

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import RuleDetail, RuleDetailStatusEnum


class DataValidationError(Exception):
    """数据验证异常"""

    def __init__(self, message: str, validation_type: str = "general", details: dict | None = None):
        super().__init__(message)
        self.validation_type = validation_type
        self.details = details or {}


class MigrationDataValidator:
    """数据迁移验证器"""

    def __init__(self, session_factory):
        """
        初始化验证器

        Args:
            session_factory: 数据库会话工厂
        """
        self.session_factory = session_factory
        logger.info("MigrationDataValidator 初始化完成")

    def validate_pre_migration(self, dataset_ids: list[int] | None = None) -> dict[str, Any]:
        """
        迁移前数据验证

        Args:
            dataset_ids: 要验证的数据集ID列表，None表示验证所有待迁移数据集

        Returns:
            Dict: 验证结果
        """
        logger.info(f"开始迁移前数据验证... (数据集: {dataset_ids or '全部'})")

        validation_result = {
            "validation_type": "pre_migration",
            "total_datasets": 0,
            "valid_datasets": 0,
            "invalid_datasets": 0,
            "validation_errors": [],
            "validation_warnings": [],
            "dataset_details": {},
            "overall_valid": True,
            "validation_time": datetime.now(),
        }

        try:
            with self.session_factory() as session:
                # 获取要验证的规则明细数据（适配新模型）
                if dataset_ids:
                    # 将dataset_ids作为rule_detail的id来查询
                    rule_details = session.query(RuleDetail).filter(RuleDetail.id.in_(dataset_ids)).all()
                else:
                    # 获取所有激活状态的规则明细
                    rule_details = session.query(RuleDetail).filter(RuleDetail.status == RuleDetailStatusEnum.ACTIVE).all()

                validation_result["total_datasets"] = len(rule_details)

                for rule_detail in rule_details:
                    detail_validation = self._validate_single_rule_detail_pre_migration(rule_detail)
                    validation_result["dataset_details"][rule_detail.id] = detail_validation

                    if detail_validation["valid"]:
                        validation_result["valid_datasets"] += 1
                    else:
                        validation_result["invalid_datasets"] += 1
                        validation_result["overall_valid"] = False
                        validation_result["validation_errors"].extend(
                            [f"规则明细 {rule_detail.id}: {error}" for error in detail_validation["errors"]]
                        )

                    validation_result["validation_warnings"].extend(
                        [f"规则明细 {rule_detail.id}: {warning}" for warning in detail_validation["warnings"]]
                    )

                logger.info(
                    f"迁移前验证完成: 有效 {validation_result['valid_datasets']}, "
                    f"无效 {validation_result['invalid_datasets']}"
                )

                return validation_result

        except Exception as e:
            logger.error(f"迁移前验证失败: {e}", exc_info=True)
            validation_result["validation_errors"].append(f"验证过程异常: {str(e)}")
            validation_result["overall_valid"] = False
            return validation_result

    def validate_post_migration(self, dataset_ids: list[int] | None = None) -> dict[str, Any]:
        """
        迁移后数据验证

        Args:
            dataset_ids: 要验证的数据集ID列表，None表示验证所有已迁移数据集

        Returns:
            Dict: 验证结果
        """
        logger.info(f"开始迁移后数据验证... (数据集: {dataset_ids or '全部'})")

        validation_result = {
            "validation_type": "post_migration",
            "total_datasets": 0,
            "valid_datasets": 0,
            "invalid_datasets": 0,
            "integrity_issues": [],
            "data_mismatches": [],
            "missing_records": [],
            "extra_records": [],
            "dataset_details": {},
            "overall_valid": True,
            "validation_time": datetime.now(),
        }

        try:
            with self.session_factory() as session:
                # 获取要验证的规则明细数据（适配新模型）
                if dataset_ids:
                    rule_details = (
                        session.query(RuleDetail)
                        .filter(
                            RuleDetail.id.in_(dataset_ids),
                            RuleDetail.status == RuleDetailStatusEnum.ACTIVE,
                        )
                        .all()
                    )
                else:
                    rule_details = (
                        session.query(RuleDetail)
                        .filter(RuleDetail.status == RuleDetailStatusEnum.ACTIVE)
                        .all()
                    )

                validation_result["total_datasets"] = len(rule_details)

                for rule_detail in rule_details:
                    detail_validation = self._validate_single_rule_detail_post_migration(session, rule_detail)
                    validation_result["dataset_details"][rule_detail.id] = detail_validation

                    if detail_validation["valid"]:
                        validation_result["valid_datasets"] += 1
                    else:
                        validation_result["invalid_datasets"] += 1
                        validation_result["overall_valid"] = False

                    # 合并验证结果
                    for key in ["integrity_issues", "data_mismatches", "missing_records", "extra_records"]:
                        validation_result[key].extend(detail_validation.get(key, []))

                logger.info(
                    f"迁移后验证完成: 有效 {validation_result['valid_datasets']}, "
                    f"无效 {validation_result['invalid_datasets']}"
                )

                return validation_result

        except Exception as e:
            logger.error(f"迁移后验证失败: {e}", exc_info=True)
            validation_result["integrity_issues"].append(f"验证过程异常: {str(e)}")
            validation_result["overall_valid"] = False
            return validation_result

    def validate_data_consistency(self, dataset_id: int) -> dict[str, Any]:
        """
        验证单个数据集的数据一致性

        Args:
            dataset_id: 数据集ID

        Returns:
            Dict: 一致性验证结果
        """
        logger.info(f"开始验证数据集 {dataset_id} 的数据一致性...")

        consistency_result = {
            "dataset_id": dataset_id,
            "consistent": True,
            "field_mismatches": [],
            "type_conversion_issues": [],
            "data_quality_issues": [],
            "validation_details": {},
        }

        try:
            with self.session_factory() as session:
                rule_detail = session.query(RuleDetail).get(dataset_id)
                if not rule_detail:
                    raise ValueError(f"规则明细 {dataset_id} 不存在")

                # 使用 RuleDetail 的 status 字段代替 migration_status
                if rule_detail.status != RuleDetailStatusEnum.ACTIVE:
                    raise ValueError(f"规则明细 {dataset_id} 尚未激活")

                # 执行数据完整性检查（简化版本，适配新模型）
                validation_details = rule_detail.validate_data()
                consistency_result["validation_details"] = validation_details

                # 设置结果状态
                consistency_result["consistent"] = validation_details["valid"]

                if not validation_details["valid"]:
                    consistency_result["data_quality_issues"].extend(validation_details["errors"])

                logger.info(
                    f"规则明细 {dataset_id} 一致性验证完成: {'通过' if consistency_result['consistent'] else '失败'}"
                )

                return consistency_result

        except Exception as e:
            logger.error(f"数据一致性验证失败: {e}", exc_info=True)
            consistency_result["consistent"] = False
            consistency_result["data_quality_issues"].append(f"验证过程异常: {str(e)}")
            return consistency_result

    def generate_validation_report(self, validation_results: list[dict[str, Any]]) -> str:
        """
        生成验证报告

        Args:
            validation_results: 验证结果列表

        Returns:
            str: 格式化的验证报告
        """
        report_lines = ["# 数据迁移验证报告", f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", ""]

        for i, result in enumerate(validation_results, 1):
            report_lines.extend(
                [
                    f"## 验证 {i}: {result.get('validation_type', 'unknown')}",
                    f"验证时间: {result.get('validation_time', 'N/A')}",
                    f"总数据集: {result.get('total_datasets', 0)}",
                    f"有效数据集: {result.get('valid_datasets', 0)}",
                    f"无效数据集: {result.get('invalid_datasets', 0)}",
                    f"整体状态: {'✅ 通过' if result.get('overall_valid', False) else '❌ 失败'}",
                    "",
                ]
            )

            # 添加错误详情
            if result.get("validation_errors"):
                report_lines.extend(["### 验证错误:", *[f"- {error}" for error in result["validation_errors"]], ""])

            if result.get("validation_warnings"):
                report_lines.extend(
                    ["### 验证警告:", *[f"- {warning}" for warning in result["validation_warnings"]], ""]
                )

        return "\n".join(report_lines)

    def _validate_single_rule_detail_pre_migration(self, rule_detail: RuleDetail) -> dict[str, Any]:
        """验证单个规则明细的迁移前状态"""
        validation_result = {"rule_detail_id": rule_detail.id, "valid": True, "errors": [], "warnings": [], "statistics": {}}

        try:
            # 基本检查：使用新模型的数据验证
            validation = rule_detail.validate_data()
            if not validation["valid"]:
                validation_result["valid"] = False
                validation_result["errors"].extend(validation["errors"])
                validation_result["warnings"].extend(validation["warnings"])
                return validation_result

            # 验证必填字段
            if not rule_detail.rule_id:
                validation_result["valid"] = False
                validation_result["errors"].append("rule_id 字段为空")

            if not rule_detail.rule_name:
                validation_result["valid"] = False
                validation_result["errors"].append("rule_name 字段为空")

            if not rule_detail.rule_key:
                validation_result["valid"] = False
                validation_result["errors"].append("rule_key 字段为空")

            # 统计信息
            validation_result["statistics"] = {
                "rule_id": rule_detail.rule_id,
                "rule_key": rule_detail.rule_key,
                "status": rule_detail.status.value if rule_detail.status else None,
                "has_extended_fields": bool(rule_detail.extended_fields),
                "created_at": rule_detail.created_at.isoformat() if rule_detail.created_at else None
            }

        except Exception as e:
            logger.error(f"验证规则明细 {rule_detail.id} 时发生错误: {e}")
            validation_result["valid"] = False
            validation_result["errors"].append(f"验证过程中发生异常: {str(e)}")

        return validation_result

    def _validate_single_rule_detail_post_migration(self, session: Session, rule_detail: RuleDetail) -> dict[str, Any]:
        """验证单个规则明细的迁移后状态（简化版本，适配新模型）"""
        validation_result = {
            "rule_detail_id": rule_detail.id,
            "valid": True,
            "integrity_issues": [],
            "data_mismatches": [],
            "missing_records": [],
            "extra_records": [],
            "statistics": {},
        }

        try:
            # 在新模型中，RuleDetail 直接存储在数据库中，无需进行迁移前后比较
            # 这里主要验证数据完整性和有效性

            # 验证规则明细本身的数据完整性
            validation_details = rule_detail.validate_data()
            if not validation_details["valid"]:
                validation_result["valid"] = False
                validation_result["integrity_issues"].extend(validation_details["errors"])
                validation_result["data_mismatches"].extend(validation_details.get("warnings", []))

            # 验证必要字段是否存在
            required_fields = ["rule_id", "rule_name", "rule_key"]
            missing_fields = []
            for field in required_fields:
                if not getattr(rule_detail, field, None):
                    missing_fields.append(field)

            if missing_fields:
                validation_result["valid"] = False
                validation_result["missing_records"].extend(missing_fields)
                validation_result["integrity_issues"].append(f"缺少必要字段: {', '.join(missing_fields)}")

            # 检查同一 rule_key 下是否有重复的 rule_id
            duplicate_rule_ids = (
                session.query(RuleDetail)
                .filter(
                    RuleDetail.rule_key == rule_detail.rule_key,
                    RuleDetail.rule_id == rule_detail.rule_id,
                    RuleDetail.id != rule_detail.id,
                    RuleDetail.status == RuleDetailStatusEnum.ACTIVE
                )
                .count()
            )

            if duplicate_rule_ids > 0:
                validation_result["valid"] = False
                validation_result["extra_records"].append(rule_detail.rule_id)
                validation_result["integrity_issues"].append(f"发现重复的 rule_id: {rule_detail.rule_id}")

            # 统计信息
            validation_result["statistics"] = {
                "rule_id": rule_detail.rule_id,
                "rule_key": rule_detail.rule_key,
                "status": rule_detail.status.value if rule_detail.status else None,
                "has_extended_fields": bool(rule_detail.extended_fields),
                "validation_passed": validation_result["valid"],
                "duplicate_count": duplicate_rule_ids,
            }

            return validation_result

        except Exception as e:
            validation_result["valid"] = False
            validation_result["integrity_issues"].append(f"验证过程异常: {str(e)}")
            return validation_result

    def _compare_field_values(self, original_item: dict[str, Any], migrated_detail: RuleDetail) -> dict[str, Any]:
        """比较原始数据和迁移后数据的字段值"""
        comparison_result = {"mismatches": [], "type_issues": [], "matches": 0, "total_fields": 0}

        # 字段映射关系
        field_mappings = {
            "rule_id": "rule_detail_id",
            "rule_name": "rule_name",
            "error_level_1": "error_level_1",
            "error_level_2": "error_level_2",
            "error_level_3": "error_level_3",
            "error_reason": "error_reason",
            "error_severity": "error_severity",
            "quality_basis": "quality_basis",
            "location_desc": "location_desc",
            "prompt_field_type": "prompt_field_type",
            "prompt_field_code": "prompt_field_code",
            "rule_category": "rule_category",
            "applicable_business": "applicable_business",
            "applicable_region": "applicable_region",
            "violation_items": "violation_items",
            "remark": "remark",
        }

        for original_field, migrated_field in field_mappings.items():
            if original_field in original_item:
                comparison_result["total_fields"] += 1
                original_value = original_item[original_field]
                migrated_value = getattr(migrated_detail, migrated_field, None)

                # 处理空值
                if original_value in [None, "", "null"] and migrated_value in [None, ""]:
                    comparison_result["matches"] += 1
                    continue

                # 字符串比较
                if isinstance(original_value, str) and isinstance(migrated_value, str):
                    if original_value.strip() == migrated_value.strip():
                        comparison_result["matches"] += 1
                    else:
                        comparison_result["mismatches"].append(
                            f"{original_field}: '{original_value}' != '{migrated_value}'"
                        )
                elif str(original_value) == str(migrated_value):
                    comparison_result["matches"] += 1
                else:
                    comparison_result["mismatches"].append(
                        f"{original_field}: '{original_value}' != '{migrated_value}'"
                    )

        return comparison_result
