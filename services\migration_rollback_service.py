"""
数据迁移回滚服务
负责处理迁移失败时的数据回滚操作
"""

import shutil
from datetime import datetime
from pathlib import Path
from typing import Any

from sqlalchemy import text
from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import MigrationStatusEnum, RuleDataSet, RuleDetail


class RollbackError(Exception):
    """回滚操作异常"""

    def __init__(self, message: str, rollback_type: str = "general", details: dict | None = None):
        super().__init__(message)
        self.rollback_type = rollback_type
        self.details = details or {}


class MigrationRollbackService:
    """数据迁移回滚服务"""

    def __init__(self, session_factory, backup_dir: str = "./migration_backups"):
        """
        初始化回滚服务

        Args:
            session_factory: 数据库会话工厂
            backup_dir: 备份文件目录
        """
        self.session_factory = session_factory
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"MigrationRollbackService 初始化完成，备份目录: {self.backup_dir}")

    def create_backup_before_migration(self, dataset_ids: list[int] | None = None) -> dict[str, Any]:
        """
        在迁移前创建数据备份

        Args:
            dataset_ids: 要备份的数据集ID列表，None表示备份所有待迁移数据集

        Returns:
            Dict: 备份结果
        """
        logger.info(f"开始创建迁移前数据备份... (数据集: {dataset_ids or '全部'})")

        backup_result = {
            "backup_id": self._generate_backup_id(),
            "backup_time": datetime.now(),
            "total_datasets": 0,
            "backed_up_datasets": 0,
            "failed_backups": 0,
            "backup_files": [],
            "errors": [],
        }

        try:
            with self.session_factory() as session:
                # 获取要备份的数据集
                if dataset_ids:
                    datasets = session.query(RuleDataSet).filter(RuleDataSet.id.in_(dataset_ids)).all()
                else:
                    datasets = (
                        session.query(RuleDataSet)
                        .filter(RuleDataSet.migration_status == MigrationStatusEnum.PENDING)
                        .all()
                    )

                backup_result["total_datasets"] = len(datasets)

                # 创建备份目录
                backup_dir = self.backup_dir / backup_result["backup_id"]
                backup_dir.mkdir(parents=True, exist_ok=True)

                for dataset in datasets:
                    try:
                        backup_file = self._backup_single_dataset(dataset, backup_dir)
                        backup_result["backup_files"].append(str(backup_file))
                        backup_result["backed_up_datasets"] += 1

                        logger.debug(f"数据集 {dataset.id} 备份完成: {backup_file}")

                    except Exception as e:
                        backup_result["failed_backups"] += 1
                        error_msg = f"备份数据集 {dataset.id} 失败: {str(e)}"
                        backup_result["errors"].append(error_msg)
                        logger.error(error_msg, exc_info=True)

                # 创建备份元数据文件
                metadata_file = backup_dir / "backup_metadata.json"
                self._save_backup_metadata(metadata_file, backup_result)

                logger.info(
                    f"数据备份完成，备份ID: {backup_result['backup_id']}, "
                    f"成功: {backup_result['backed_up_datasets']}, "
                    f"失败: {backup_result['failed_backups']}"
                )

                return backup_result

        except Exception as e:
            logger.error(f"创建数据备份失败: {e}", exc_info=True)
            backup_result["errors"].append(f"备份过程异常: {str(e)}")
            return backup_result

    def rollback_migration(
        self, dataset_ids: list[int] | None = None, backup_id: str | None = None, force: bool = False
    ) -> dict[str, Any]:
        """
        回滚迁移操作

        Args:
            dataset_ids: 要回滚的数据集ID列表，None表示回滚所有
            backup_id: 备份ID，用于恢复原始数据
            force: 是否强制回滚（忽略某些检查）

        Returns:
            Dict: 回滚结果
        """
        logger.info(f"开始回滚迁移操作... (数据集: {dataset_ids or '全部'}, " f"备份ID: {backup_id}, 强制: {force})")

        rollback_result = {
            "rollback_id": self._generate_rollback_id(),
            "rollback_time": datetime.now(),
            "total_datasets": 0,
            "rollback_datasets": 0,
            "failed_rollbacks": 0,
            "deleted_details": 0,
            "restored_datasets": 0,
            "operations": [],
            "errors": [],
        }

        try:
            with self.session_factory() as session:
                # 获取要回滚的数据集
                datasets = self._get_rollback_datasets(session, dataset_ids, force)
                rollback_result["total_datasets"] = len(datasets)

                # 执行回滚操作
                for dataset in datasets:
                    try:
                        dataset_rollback = self._rollback_single_dataset(session, dataset, backup_id, force)

                        rollback_result["rollback_datasets"] += 1
                        rollback_result["deleted_details"] += dataset_rollback["deleted_details"]
                        rollback_result["operations"].extend(dataset_rollback["operations"])

                        if dataset_rollback["restored"]:
                            rollback_result["restored_datasets"] += 1

                        logger.info(f"数据集 {dataset.id} 回滚成功")

                    except Exception as e:
                        rollback_result["failed_rollbacks"] += 1
                        error_msg = f"回滚数据集 {dataset.id} 失败: {str(e)}"
                        rollback_result["errors"].append(error_msg)
                        logger.error(error_msg, exc_info=True)

                        if not force:
                            # 如果不是强制模式，遇到错误就停止
                            break

                session.commit()

                logger.info(
                    f"回滚操作完成，回滚ID: {rollback_result['rollback_id']}, "
                    f"成功: {rollback_result['rollback_datasets']}, "
                    f"失败: {rollback_result['failed_rollbacks']}"
                )

                return rollback_result

        except Exception as e:
            logger.error(f"回滚操作失败: {e}", exc_info=True)
            rollback_result["errors"].append(f"回滚过程异常: {str(e)}")
            return rollback_result

    def verify_rollback(self, rollback_id: str) -> dict[str, Any]:
        """
        验证回滚操作的结果

        Args:
            rollback_id: 回滚操作ID

        Returns:
            Dict: 验证结果
        """
        logger.info(f"开始验证回滚操作结果... (回滚ID: {rollback_id})")

        verification_result = {
            "rollback_id": rollback_id,
            "verification_time": datetime.now(),
            "verification_passed": True,
            "issues": [],
            "statistics": {},
        }

        try:
            with self.session_factory() as session:
                # 检查是否还有已完成迁移的数据集
                completed_datasets = (
                    session.query(RuleDataSet)
                    .filter(RuleDataSet.migration_status == MigrationStatusEnum.COMPLETED)
                    .count()
                )

                # 检查是否还有迁移后的明细数据
                remaining_details = session.query(RuleDetail).count()

                # 检查是否有处于中间状态的数据集
                in_progress_datasets = (
                    session.query(RuleDataSet)
                    .filter(RuleDataSet.migration_status == MigrationStatusEnum.IN_PROGRESS)
                    .count()
                )

                verification_result["statistics"] = {
                    "completed_datasets": completed_datasets,
                    "remaining_details": remaining_details,
                    "in_progress_datasets": in_progress_datasets,
                }

                # 验证规则
                if completed_datasets > 0:
                    verification_result["verification_passed"] = False
                    verification_result["issues"].append(f"仍有 {completed_datasets} 个数据集处于已完成状态")

                if remaining_details > 0:
                    verification_result["verification_passed"] = False
                    verification_result["issues"].append(f"仍有 {remaining_details} 条明细数据未清理")

                if in_progress_datasets > 0:
                    verification_result["verification_passed"] = False
                    verification_result["issues"].append(f"有 {in_progress_datasets} 个数据集处于迁移中状态")

                logger.info(f"回滚验证完成: {'通过' if verification_result['verification_passed'] else '失败'}")

                return verification_result

        except Exception as e:
            logger.error(f"回滚验证失败: {e}", exc_info=True)
            verification_result["verification_passed"] = False
            verification_result["issues"].append(f"验证过程异常: {str(e)}")
            return verification_result

    def list_backups(self) -> list[dict[str, Any]]:
        """
        列出所有可用的备份

        Returns:
            List[Dict]: 备份列表
        """
        backups = []

        try:
            if not self.backup_dir.exists():
                return backups

            for backup_dir in self.backup_dir.iterdir():
                if backup_dir.is_dir():
                    metadata_file = backup_dir / "backup_metadata.json"
                    if metadata_file.exists():
                        try:
                            import json

                            with open(metadata_file, "r", encoding="utf-8") as f:
                                metadata = json.load(f)
                            backups.append(metadata)
                        except Exception as e:
                            logger.warning(f"读取备份元数据失败 {metadata_file}: {e}")

            # 按时间排序
            backups.sort(key=lambda x: x.get("backup_time", ""), reverse=True)

            return backups

        except Exception as e:
            logger.error(f"列出备份失败: {e}", exc_info=True)
            return backups

    def cleanup_old_backups(self, keep_days: int = 30) -> dict[str, Any]:
        """
        清理旧的备份文件

        Args:
            keep_days: 保留天数

        Returns:
            Dict: 清理结果
        """
        logger.info(f"开始清理 {keep_days} 天前的备份文件...")

        cleanup_result = {
            "cleanup_time": datetime.now(),
            "total_backups": 0,
            "deleted_backups": 0,
            "kept_backups": 0,
            "freed_space_mb": 0,
            "errors": [],
        }

        try:
            cutoff_time = datetime.now().timestamp() - (keep_days * 24 * 3600)

            for backup_dir in self.backup_dir.iterdir():
                if backup_dir.is_dir():
                    cleanup_result["total_backups"] += 1

                    # 检查备份时间
                    if backup_dir.stat().st_mtime < cutoff_time:
                        try:
                            # 计算目录大小
                            dir_size = sum(f.stat().st_size for f in backup_dir.rglob("*") if f.is_file())

                            # 删除备份目录
                            shutil.rmtree(backup_dir)

                            cleanup_result["deleted_backups"] += 1
                            cleanup_result["freed_space_mb"] += dir_size / (1024 * 1024)

                            logger.debug(f"删除旧备份: {backup_dir}")

                        except Exception as e:
                            error_msg = f"删除备份 {backup_dir} 失败: {str(e)}"
                            cleanup_result["errors"].append(error_msg)
                            logger.error(error_msg)
                    else:
                        cleanup_result["kept_backups"] += 1

            logger.info(
                f"备份清理完成，删除: {cleanup_result['deleted_backups']}, "
                f"保留: {cleanup_result['kept_backups']}, "
                f"释放空间: {cleanup_result['freed_space_mb']:.2f}MB"
            )

            return cleanup_result

        except Exception as e:
            logger.error(f"清理备份失败: {e}", exc_info=True)
            cleanup_result["errors"].append(f"清理过程异常: {str(e)}")
            return cleanup_result

    def _generate_backup_id(self) -> str:
        """生成备份ID"""
        return f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    def _generate_rollback_id(self) -> str:
        """生成回滚ID"""
        return f"rollback_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    def _backup_single_dataset(self, dataset: RuleDataSet, backup_dir: Path) -> Path:
        """备份单个数据集"""
        import json

        backup_data = {
            "dataset_id": dataset.id,
            "base_rule_id": dataset.base_rule_id,
            "data_set": dataset.data_set,
            "version": dataset.version,
            "is_active": dataset.is_active,
            "uploaded_by": dataset.uploaded_by,
            "created_at": dataset.created_at.isoformat() if dataset.created_at else None,
            "migration_status": dataset.migration_status.value if dataset.migration_status else None,
            "migration_timestamp": dataset.migration_timestamp.isoformat() if dataset.migration_timestamp else None,
        }

        backup_file = backup_dir / f"dataset_{dataset.id}.json"
        with open(backup_file, "w", encoding="utf-8") as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)

        return backup_file

    def _save_backup_metadata(self, metadata_file: Path, backup_result: dict[str, Any]):
        """保存备份元数据"""
        import json

        metadata = {
            "backup_id": backup_result["backup_id"],
            "backup_time": backup_result["backup_time"].isoformat(),
            "total_datasets": backup_result["total_datasets"],
            "backed_up_datasets": backup_result["backed_up_datasets"],
            "failed_backups": backup_result["failed_backups"],
            "backup_files": backup_result["backup_files"],
            "errors": backup_result["errors"],
        }

        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

    def _get_rollback_datasets(
        self, session: Session, dataset_ids: list[int] | None, force: bool
    ) -> list[RuleDataSet]:
        """获取要回滚的数据集"""
        if dataset_ids:
            datasets = session.query(RuleDataSet).filter(RuleDataSet.id.in_(dataset_ids)).all()
        else:
            if force:
                # 强制模式：回滚所有非待迁移状态的数据集
                datasets = (
                    session.query(RuleDataSet).filter(RuleDataSet.migration_status != MigrationStatusEnum.PENDING).all()
                )
            else:
                # 正常模式：只回滚已完成和失败的数据集
                datasets = (
                    session.query(RuleDataSet)
                    .filter(
                        RuleDataSet.migration_status.in_([MigrationStatusEnum.COMPLETED, MigrationStatusEnum.FAILED])
                    )
                    .all()
                )

        return datasets

    def _rollback_single_dataset(
        self, session: Session, dataset: RuleDataSet, backup_id: str | None, force: bool
    ) -> dict[str, Any]:
        """回滚单个数据集"""
        rollback_result = {"dataset_id": dataset.id, "deleted_details": 0, "restored": False, "operations": []}

        try:
            # 1. 删除迁移的明细数据
            deleted_count = session.query(RuleDetail).filter(RuleDetail.dataset_id == dataset.id).delete()

            rollback_result["deleted_details"] = deleted_count
            rollback_result["operations"].append(f"删除了 {deleted_count} 条明细数据")

            # 2. 恢复原始数据（如果有备份）
            if backup_id:
                restored = self._restore_dataset_from_backup(session, dataset, backup_id)
                if restored:
                    rollback_result["restored"] = True
                    rollback_result["operations"].append("从备份恢复原始数据")

            # 3. 重置迁移状态
            dataset.migration_status = MigrationStatusEnum.PENDING
            dataset.migration_timestamp = None
            rollback_result["operations"].append("重置迁移状态为待迁移")

            session.flush()

            return rollback_result

        except Exception as e:
            logger.error(f"回滚数据集 {dataset.id} 失败: {e}", exc_info=True)
            raise RollbackError(f"回滚数据集 {dataset.id} 失败: {str(e)}", "dataset_rollback") from None

    def _restore_dataset_from_backup(self, session: Session, dataset: RuleDataSet, backup_id: str) -> bool:
        """从备份恢复数据集"""
        try:
            import json

            backup_dir = self.backup_dir / backup_id
            backup_file = backup_dir / f"dataset_{dataset.id}.json"

            if not backup_file.exists():
                logger.warning(f"备份文件不存在: {backup_file}")
                return False

            with open(backup_file, "r", encoding="utf-8") as f:
                backup_data = json.load(f)

            # 恢复数据集字段
            dataset.data_set = backup_data.get("data_set")
            dataset.version = backup_data.get("version", dataset.version)
            dataset.is_active = backup_data.get("is_active", dataset.is_active)
            dataset.uploaded_by = backup_data.get("uploaded_by", dataset.uploaded_by)

            # 恢复时间字段
            if backup_data.get("created_at"):
                try:
                    dataset.created_at = datetime.fromisoformat(backup_data["created_at"])
                except ValueError:
                    pass

            logger.info(f"从备份恢复数据集 {dataset.id} 成功")
            return True

        except Exception as e:
            logger.error(f"从备份恢复数据集 {dataset.id} 失败: {e}", exc_info=True)
            return False

    def emergency_rollback(self) -> dict[str, Any]:
        """
        紧急回滚：清理所有迁移数据，恢复到迁移前状态

        Returns:
            Dict: 紧急回滚结果
        """
        logger.warning("开始执行紧急回滚操作...")

        emergency_result = {
            "emergency_rollback_time": datetime.now(),
            "total_details_deleted": 0,
            "total_datasets_reset": 0,
            "operations": [],
            "errors": [],
        }

        try:
            with self.session_factory() as session:
                # 1. 删除所有迁移的明细数据
                total_details = session.query(RuleDetail).count()
                if total_details > 0:
                    session.query(RuleDetail).delete()
                    emergency_result["total_details_deleted"] = total_details
                    emergency_result["operations"].append(f"删除了所有 {total_details} 条明细数据")

                # 2. 重置所有数据集的迁移状态
                datasets_to_reset = (
                    session.query(RuleDataSet).filter(RuleDataSet.migration_status != MigrationStatusEnum.PENDING).all()
                )

                for dataset in datasets_to_reset:
                    dataset.migration_status = MigrationStatusEnum.PENDING
                    dataset.migration_timestamp = None

                emergency_result["total_datasets_reset"] = len(datasets_to_reset)
                emergency_result["operations"].append(f"重置了 {len(datasets_to_reset)} 个数据集的迁移状态")

                # 3. 重置自增ID（可选，谨慎操作）
                try:
                    session.execute(text("ALTER TABLE rule_details AUTO_INCREMENT = 1"))
                    emergency_result["operations"].append("重置了 rule_details 表的自增ID")
                except Exception as e:
                    emergency_result["errors"].append(f"重置自增ID失败: {str(e)}")

                session.commit()

                logger.warning(
                    f"紧急回滚完成，删除明细: {emergency_result['total_details_deleted']}, "
                    f"重置数据集: {emergency_result['total_datasets_reset']}"
                )

                return emergency_result

        except Exception as e:
            logger.error(f"紧急回滚失败: {e}", exc_info=True)
            emergency_result["errors"].append(f"紧急回滚过程异常: {str(e)}")
            return emergency_result
