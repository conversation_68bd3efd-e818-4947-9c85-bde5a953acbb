<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
      class="rule-detail-form"
    >
      <!-- 基础信息 -->
      <div class="form-section">
        <h4 class="section-title">基础信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="明细ID" prop="rule_detail_id">
              <el-input
                v-model="formData.rule_detail_id"
                placeholder="将根据规则名称自动生成"
                readonly
                :disabled="true"
              />
              <div class="field-hint">
                <el-icon><InfoFilled /></el-icon>
                明细ID将根据规则名称自动生成（MD5值）
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则名称" prop="rule_name">
              <el-input 
                v-model="formData.rule_name" 
                placeholder="请输入规则名称"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规则类别" prop="rule_category">
              <el-select 
                v-model="formData.rule_category" 
                placeholder="请选择规则类别"
                style="width: 100%"
              >
                <el-option label="数据质量" value="data_quality" />
                <el-option label="业务规则" value="business_rule" />
                <el-option label="合规检查" value="compliance_check" />
                <el-option label="安全验证" value="security_validation" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select 
                v-model="formData.status" 
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="活跃" value="ACTIVE" />
                <el-option label="非活跃" value="INACTIVE" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="默认选用">
              <el-switch 
                v-model="formData.default_selected"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="涉及金额" prop="involved_amount">
              <el-input-number
                v-model="formData.involved_amount"
                :precision="2"
                :min="0"
                :max="999999999.99"
                style="width: 100%"
                placeholder="请输入涉及金额"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 错误分类 -->
      <div class="form-section">
        <h4 class="section-title">错误分类</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="一级错误" prop="error_level_1">
              <el-select 
                v-model="formData.error_level_1" 
                placeholder="请选择一级错误"
                style="width: 100%"
              >
                <el-option label="数据错误" value="data_error" />
                <el-option label="逻辑错误" value="logic_error" />
                <el-option label="格式错误" value="format_error" />
                <el-option label="业务错误" value="business_error" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="二级错误" prop="error_level_2">
              <el-input 
                v-model="formData.error_level_2" 
                placeholder="请输入二级错误类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="三级错误" prop="error_level_3">
              <el-input 
                v-model="formData.error_level_3" 
                placeholder="请输入三级错误类型"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="错误原因" prop="error_reason">
          <el-input
            v-model="formData.error_reason"
            type="textarea"
            :rows="3"
            placeholder="请描述错误原因"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="错误程度" prop="error_severity">
              <el-select 
                v-model="formData.error_severity" 
                placeholder="请选择错误程度"
                style="width: 100%"
              >
                <el-option label="轻微" value="minor" />
                <el-option label="一般" value="normal" />
                <el-option label="严重" value="serious" />
                <el-option label="致命" value="critical" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适用业务" prop="applicable_business">
              <el-input 
                v-model="formData.applicable_business" 
                placeholder="请输入适用业务"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 质控信息 -->
      <div class="form-section">
        <h4 class="section-title">质控信息</h4>
        <el-form-item label="质控依据" prop="quality_basis">
          <el-input
            v-model="formData.quality_basis"
            type="textarea"
            :rows="2"
            placeholder="请输入质控依据或参考资料"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="位置描述" prop="location_desc">
              <el-input 
                v-model="formData.location_desc" 
                placeholder="请输入具体位置描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适用地区" prop="applicable_region">
              <el-input 
                v-model="formData.applicable_region" 
                placeholder="请输入适用地区"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="提示字段类型" prop="prompt_field_type">
              <el-input 
                v-model="formData.prompt_field_type" 
                placeholder="请输入提示字段类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="提示字段编码" prop="prompt_field_code">
              <el-input 
                v-model="formData.prompt_field_code" 
                placeholder="请输入提示字段编码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="提示字段序号" prop="prompt_field_seq">
              <el-input-number
                v-model="formData.prompt_field_seq"
                :min="1"
                style="width: 100%"
                placeholder="请输入序号"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 统计信息 -->
      <div class="form-section">
        <h4 class="section-title">统计信息</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="使用数量" prop="usage_quantity">
              <el-input-number
                v-model="formData.usage_quantity"
                :min="0"
                style="width: 100%"
                placeholder="使用数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="违规数量" prop="violation_quantity">
              <el-input-number
                v-model="formData.violation_quantity"
                :min="0"
                style="width: 100%"
                placeholder="违规数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="使用天数" prop="usage_days">
              <el-input-number
                v-model="formData.usage_days"
                :min="0"
                style="width: 100%"
                placeholder="使用天数"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="违规天数" prop="violation_days">
              <el-input-number
                v-model="formData.violation_days"
                :min="0"
                style="width: 100%"
                placeholder="违规天数"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="违规项目" prop="violation_items">
          <el-input
            v-model="formData.violation_items"
            type="textarea"
            :rows="2"
            placeholder="请输入违规项目描述"
          />
        </el-form-item>
      </div>

      <!-- 备注信息 -->
      <div class="form-section">
        <h4 class="section-title">备注信息</h4>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ mode === 'create' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import CryptoJS from 'crypto-js'
import { createRuleDetail, updateRuleDetail } from '../../api/ruleDetails'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ruleKey: {
    type: String,
    required: true
  },
  mode: {
    type: String,
    default: 'create', // 'create' | 'edit'
    validator: (value) => ['create', 'edit'].includes(value)
  },
  detailData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success', 'cancel'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  return props.mode === 'create' ? '新增规则明细' : '编辑规则明细'
})

// 表单数据
const formData = ref({
  rule_detail_id: '',
  rule_name: '',
  rule_category: '',
  status: 'ACTIVE',
  default_selected: false,
  involved_amount: null,
  error_level_1: '',
  error_level_2: '',
  error_level_3: '',
  error_reason: '',
  error_severity: '',
  applicable_business: '',
  quality_basis: '',
  location_desc: '',
  applicable_region: '',
  prompt_field_type: '',
  prompt_field_code: '',
  prompt_field_seq: null,
  usage_quantity: null,
  violation_quantity: null,
  usage_days: null,
  violation_days: null,
  violation_items: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  rule_detail_id: [
    { required: true, message: '明细ID不能为空', trigger: 'blur' }
  ],
  rule_name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// MD5生成函数
const generateRuleDetailId = (ruleName) => {
  if (!ruleName || ruleName.trim() === '') {
    return ''
  }
  return CryptoJS.MD5(ruleName.trim()).toString()
}

// 方法定义
const initFormData = () => {
  if (props.mode === 'edit' && props.detailData) {
    // 编辑模式：填充现有数据
    Object.keys(formData.value).forEach(key => {
      if (props.detailData[key] !== undefined) {
        formData.value[key] = props.detailData[key]
      }
    })
  } else {
    // 创建模式：重置表单
    Object.keys(formData.value).forEach(key => {
      if (key === 'status') {
        formData.value[key] = 'ACTIVE'
      } else if (key === 'default_selected') {
        formData.value[key] = false
      } else if (['involved_amount', 'prompt_field_seq', 'usage_quantity', 'violation_quantity', 'usage_days', 'violation_days'].includes(key)) {
        formData.value[key] = null
      } else {
        formData.value[key] = ''
      }
    })
  }
}

const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData = { ...formData.value }
    
    // 调用API
    if (props.mode === 'create') {
      await createRuleDetail(props.ruleKey, submitData)
    } else {
      await updateRuleDetail(props.ruleKey, props.detailData.id, submitData)
    }
    
    ElMessage.success(props.mode === 'create' ? '创建成功' : '更新成功')
    emit('success')
    
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败：' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    nextTick(() => {
      initFormData()
      // 清除验证状态
      formRef.value?.clearValidate()
    })
  }
})

watch(() => props.detailData, () => {
  if (props.modelValue) {
    initFormData()
  }
})

// 监听规则名称变化，自动生成明细ID
watch(() => formData.value.rule_name, (newName) => {
  if (props.mode === 'create') {
    formData.value.rule_detail_id = generateRuleDetailId(newName)
  }
}, { immediate: false })
</script>

<style scoped>
.rule-detail-form {
  max-height: 600px;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.field-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.field-hint .el-icon {
  font-size: 14px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.dialog-footer {
  text-align: right;
}

/* 表单项样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}
</style>
