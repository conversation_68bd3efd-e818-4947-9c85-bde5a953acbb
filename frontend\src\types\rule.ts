/**
 * 规则相关的TypeScript类型定义
 */

// 规则状态枚举
export enum RuleStatus {
  NEW = 'NEW',
  CHANGED = 'CHANGED', 
  READY = 'READY',
  DEPRECATED = 'DEPRECATED'
}

// 规则基础接口
export interface Rule {
  rule_key: string
  rule_name: string
  status: RuleStatus
  rule_type?: string
  description?: string
  created_at: string
  updated_at: string
  version?: string
}

// 规则详情接口
export interface RuleDetail extends Rule {
  parameters?: RuleParameter[]
  validation_rules?: ValidationRule[]
  examples?: RuleExample[]
  dependencies?: string[]
  tags?: string[]
}

// 规则参数接口
export interface RuleParameter {
  name_en: string
  name_cn: string
  type: ParameterType
  required: boolean
  description?: string
  default_value?: any
  constraints?: ParameterConstraints
  example?: any
}

// 参数类型枚举
export enum ParameterType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  DATETIME = 'datetime',
  ARRAY = 'array',
  OBJECT = 'object'
}

// 参数约束接口
export interface ParameterConstraints {
  min_length?: number
  max_length?: number
  min_value?: number
  max_value?: number
  pattern?: string
  enum_values?: any[]
  format?: string
}

// 验证规则接口
export interface ValidationRule {
  field: string
  rule_type: 'required' | 'format' | 'range' | 'custom'
  rule_value: any
  error_message: string
}

// 规则示例接口
export interface RuleExample {
  title: string
  description?: string
  input_data: Record<string, any>
  expected_output: any
  notes?: string
}

// 规则统计接口
export interface RuleStatistics {
  total_executions: number
  success_rate: number
  avg_execution_time: number
  last_execution: string
  error_count: number
  last_error?: string
  performance_metrics?: PerformanceMetrics
}

// 性能指标接口
export interface PerformanceMetrics {
  min_time: number
  max_time: number
  p50_time: number
  p95_time: number
  p99_time: number
  memory_usage: number
  cpu_usage: number
}

// 规则Schema接口
export interface RuleSchema {
  name_en: string
  name_cn: string
  type: ParameterType
  required: boolean
  description?: string
  constraints?: ParameterConstraints
  example?: any
}

// 规则搜索参数接口
export interface RuleSearchParams {
  keyword?: string
  status?: RuleStatus[]
  rule_type?: string[]
  tags?: string[]
  date_range?: {
    start: string
    end: string
  }
  page?: number
  page_size?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 规则搜索结果接口
export interface RuleSearchResult {
  rules: Rule[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 规则状态统计接口
export interface RuleStatusCounts {
  [RuleStatus.NEW]: number
  [RuleStatus.CHANGED]: number
  [RuleStatus.READY]: number
  [RuleStatus.DEPRECATED]: number
  total: number
}

// 规则摘要接口
export interface RuleSummary {
  total: number
  ready: number
  new: number
  changed: number
  deprecated: number
  last_update: string
}

// 规则操作权限接口
export interface RulePermissions {
  can_view: boolean
  can_edit: boolean
  can_delete: boolean
  can_download_template: boolean
  can_upload_data: boolean
  can_execute: boolean
}

// 规则版本接口
export interface RuleVersion {
  version: string
  created_at: string
  created_by: string
  changes: string[]
  is_current: boolean
}

// 规则依赖接口
export interface RuleDependency {
  rule_key: string
  rule_name: string
  dependency_type: 'required' | 'optional' | 'conflict'
  description?: string
}

// 规则标签接口
export interface RuleTag {
  name: string
  color?: string
  description?: string
  count?: number
}

// 规则分类接口
export interface RuleCategory {
  id: string
  name: string
  description?: string
  parent_id?: string
  children?: RuleCategory[]
  rule_count?: number
}

// 规则模板接口
export interface RuleTemplate {
  rule_key: string
  template_name: string
  file_format: 'xlsx' | 'csv' | 'json'
  columns: TemplateColumn[]
  sample_data?: Record<string, any>[]
  download_url?: string
}

// 模板列接口
export interface TemplateColumn {
  name: string
  display_name: string
  type: ParameterType
  required: boolean
  description?: string
  example?: any
  validation?: ValidationRule[]
}
