import { ref, computed, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash-es'
import { useRuleDetailsStore } from '@/stores/ruleDetails'
import {
  getRuleDetailsList,
  getRuleDetailsStatistics,
  exportRuleDetails,
  searchRuleDetails
} from '@/api/ruleDetails'

/**
 * 规则明细列表管理 Composable
 * @param {Ref<string>} ruleKey - 规则键
 */
export function useRuleDetailsList(ruleKey) {
  const store = useRuleDetailsStore()

  // 响应式状态
  const loading = ref(false)
  const statsLoading = ref(false)
  const searchKeyword = ref('')
  const filters = reactive({
    status: '',
    error_level_1: '',
    error_level_2: '',
    rule_category: '',
    date_range: []
  })

  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  })

  const sortConfig = reactive({
    sortBy: 'created_at',
    sortOrder: 'desc'
  })

  // 规则明细数据
  const ruleDetails = ref([])
  const statsData = ref({
    total: 0,
    active: 0,
    inactive: 0,
    deleted: 0,
    error_counts: {}
  })

  // 过滤器选项配置
  const filterOptions = computed(() => ({
    status: [
      { label: '全部状态', value: '' },
      { label: '活跃', value: 'ACTIVE' },
      { label: '非活跃', value: 'INACTIVE' },
      { label: '已删除', value: 'DELETED' }
    ],
    error_level_1: [
      { label: '全部一级错误', value: '' },
      { label: '数据完整性错误', value: 'DATA_INTEGRITY' },
      { label: '业务逻辑错误', value: 'BUSINESS_LOGIC' },
      { label: '格式错误', value: 'FORMAT_ERROR' },
      { label: '合规性错误', value: 'COMPLIANCE' }
    ],
    error_level_2: [
      { label: '全部二级错误', value: '' },
      { label: '必填字段缺失', value: 'REQUIRED_FIELD_MISSING' },
      { label: '数据类型错误', value: 'DATA_TYPE_ERROR' },
      { label: '数值范围错误', value: 'VALUE_RANGE_ERROR' },
      { label: '关联性错误', value: 'RELATIONSHIP_ERROR' }
    ],
    rule_category: [
      { label: '全部类别', value: '' },
      { label: '医保规则', value: 'MEDICAL_INSURANCE' },
      { label: '诊疗规则', value: 'DIAGNOSIS_TREATMENT' },
      { label: '药品规则', value: 'MEDICATION' },
      { label: '费用规则', value: 'COST' }
    ]
  }))

  // 表格列配置
  const tableColumns = computed(() => [
    {
      prop: 'rule_detail_id',
      label: '规则ID',
      width: 120,
      sortable: true
    },
    {
      prop: 'rule_name',
      label: '规则名称',
      minWidth: 200,
      sortable: true,
      showOverflowTooltip: true
    },
    {
      prop: 'error_level_1',
      label: '一级错误',
      width: 120,
      sortable: true
    },
    {
      prop: 'error_level_2',
      label: '二级错误',
      width: 120,
      sortable: true
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      sortable: true
    },
    {
      prop: 'created_at',
      label: '创建时间',
      width: 160,
      sortable: true
    },
    {
      prop: 'actions',
      label: '操作',
      width: 150,
      fixed: 'right'
    }
  ])

  // 构建查询参数
  const buildQueryParams = () => {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      sort_by: sortConfig.sortBy,
      sort_order: sortConfig.sortOrder
    }

    // 添加搜索关键词
    if (searchKeyword.value.trim()) {
      params.search = searchKeyword.value.trim()
    }

    // 添加过滤条件
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key] !== '') {
        if (key === 'date_range' && Array.isArray(filters[key]) && filters[key].length === 2) {
          params.start_date = filters[key][0]
          params.end_date = filters[key][1]
        } else {
          params[key] = filters[key]
        }
      }
    })

    return params
  }

  // 获取规则明细列表数据
  const fetchData = async (showLoading = true) => {
    if (!ruleKey.value) {
      console.warn('规则键为空，无法获取数据')
      return
    }

    try {
      if (showLoading) {
        loading.value = true
      }

      const params = buildQueryParams()
      const response = await getRuleDetailsList(ruleKey.value, params)

      if (response.success) {
        ruleDetails.value = response.data.items || []
        pagination.total = response.data.total || 0

        // 更新store中的数据
        store.setRuleDetails(ruleDetails.value)
      } else {
        throw new Error(response.message || '获取数据失败')
      }
    } catch (error) {
      console.error('获取规则明细列表失败:', error)
      ElMessage.error('获取数据失败，请稍后重试')
      ruleDetails.value = []
      pagination.total = 0
    } finally {
      loading.value = false
    }
  }

  // 获取统计数据
  const fetchStats = async () => {
    if (!ruleKey.value) return

    try {
      statsLoading.value = true
      const response = await getRuleDetailsStatistics(ruleKey.value)

      if (response.success) {
        statsData.value = response.data
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    } finally {
      statsLoading.value = false
    }
  }

  // 防抖搜索处理
  const debouncedSearch = debounce(() => {
    pagination.page = 1 // 重置到第一页
    fetchData()
  }, 300)

  // 处理搜索
  const handleSearch = (keyword) => {
    searchKeyword.value = keyword
    debouncedSearch()
  }

  // 处理过滤器变化
  const handleFilterChange = (newFilters) => {
    Object.assign(filters, newFilters)
    pagination.page = 1 // 重置到第一页
    fetchData()
  }

  // 处理分页变化
  const handlePageChange = ({ page, pageSize }) => {
    pagination.page = page
    pagination.pageSize = pageSize
    fetchData()
  }

  // 处理排序
  const handleSort = ({ prop, order }) => {
    sortConfig.sortBy = prop
    sortConfig.sortOrder = order === 'ascending' ? 'asc' : 'desc'
    pagination.page = 1 // 重置到第一页
    fetchData()
  }

  // 刷新数据
  const handleRefresh = async () => {
    await Promise.all([
      fetchData(true),
      fetchStats()
    ])
    ElMessage.success('刷新成功')
  }

  // 导出数据
  const handleExport = async () => {
    if (!ruleKey.value) {
      ElMessage.warning('规则键为空，无法导出')
      return
    }

    try {
      loading.value = true
      const params = buildQueryParams()
      // 导出时不分页，获取所有数据
      delete params.page
      delete params.page_size

      await exportRuleDetails(ruleKey.value, params)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 监听规则键变化
  watch(ruleKey, (newRuleKey) => {
    if (newRuleKey) {
      // 重置状态
      pagination.page = 1
      searchKeyword.value = ''
      Object.keys(filters).forEach(key => {
        filters[key] = Array.isArray(filters[key]) ? [] : ''
      })

      // 获取数据
      fetchData()
      fetchStats()
    }
  }, { immediate: true })

  return {
    // 响应式数据
    ruleDetails,
    loading,
    statsData,
    statsLoading,
    searchKeyword,
    filters,
    pagination,
    filterOptions,
    tableColumns,

    // 方法
    fetchData,
    fetchStats,
    handleSearch,
    handleFilterChange,
    handlePageChange,
    handleSort,
    handleRefresh,
    handleExport
  }
}
