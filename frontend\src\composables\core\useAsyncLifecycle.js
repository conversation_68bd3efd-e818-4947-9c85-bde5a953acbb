/**
 * 异步状态生命周期管理
 * 提供异步操作的生命周期钩子、副作用管理和状态转换监听
 */

import { ref, watch, onUnmounted, nextTick } from 'vue'
import { useAppStore } from '@/stores/app'
import { AsyncStates } from '@/types/state'

/**
 * 生命周期事件类型
 */
export const LifecycleEvents = {
  BEFORE_EXECUTE: 'beforeExecute',
  AFTER_EXECUTE: 'afterExecute',
  ON_SUCCESS: 'onSuccess',
  ON_ERROR: 'onError',
  ON_RETRY: 'onRetry',
  ON_CANCEL: 'onCancel',
  ON_RESET: 'onReset',
  STATE_CHANGE: 'stateChange'
}

/**
 * 默认生命周期配置
 */
const DEFAULT_LIFECYCLE_CONFIG = {
  enableLogging: process.env.NODE_ENV === 'development',
  enableMetrics: true,
  enableGlobalIntegration: true,
  autoCleanup: true,
  maxHistorySize: 100
}

/**
 * 异步状态生命周期管理
 * @param {Object} asyncStateInstance - useAsyncState 实例
 * @param {Object} config - 生命周期配置
 * @returns {Object} 生命周期管理实例
 */
export function useAsyncLifecycle(asyncStateInstance, config = {}) {
  const finalConfig = { ...DEFAULT_LIFECYCLE_CONFIG, ...config }
  const appStore = useAppStore()
  
  // 生命周期状态
  const lifecycleHistory = ref([])
  const activeHooks = ref(new Map())
  const metrics = ref({
    totalExecutions: 0,
    successCount: 0,
    errorCount: 0,
    retryCount: 0,
    averageExecutionTime: 0,
    lastExecutionTime: null
  })

  // 执行时间追踪
  const executionStartTime = ref(null)
  const executionEndTime = ref(null)

  // ==================== 生命周期钩子注册 ====================

  /**
   * 注册生命周期钩子
   * @param {string} event - 事件名称
   * @param {Function} handler - 事件处理器
   * @param {Object} options - 钩子选项
   * @returns {Function} 取消注册函数
   */
  const onLifecycle = (event, handler, options = {}) => {
    const hookId = `${event}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const hookConfig = {
      id: hookId,
      event,
      handler,
      once: options.once || false,
      priority: options.priority || 0,
      enabled: options.enabled !== false,
      context: options.context || {}
    }

    if (!activeHooks.value.has(event)) {
      activeHooks.value.set(event, [])
    }

    const hooks = activeHooks.value.get(event)
    hooks.push(hookConfig)
    
    // 按优先级排序
    hooks.sort((a, b) => b.priority - a.priority)

    if (finalConfig.enableLogging) {
      console.debug(`[AsyncLifecycle] Hook registered: ${event}`, { hookId, options })
    }

    // 返回取消注册函数
    return () => offLifecycle(event, hookId)
  }

  /**
   * 取消生命周期钩子
   * @param {string} event - 事件名称
   * @param {string} hookId - 钩子ID
   */
  const offLifecycle = (event, hookId) => {
    const hooks = activeHooks.value.get(event)
    if (hooks) {
      const index = hooks.findIndex(hook => hook.id === hookId)
      if (index > -1) {
        hooks.splice(index, 1)
        if (finalConfig.enableLogging) {
          console.debug(`[AsyncLifecycle] Hook unregistered: ${event}`, { hookId })
        }
      }
    }
  }

  /**
   * 触发生命周期事件
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  const emitLifecycle = async (event, data = {}) => {
    const hooks = activeHooks.value.get(event) || []
    const enabledHooks = hooks.filter(hook => hook.enabled)

    if (enabledHooks.length === 0) return

    const eventData = {
      event,
      data,
      timestamp: Date.now(),
      asyncState: {
        state: asyncStateInstance.state.value,
        isLoading: asyncStateInstance.isLoading.value,
        isSuccess: asyncStateInstance.isSuccess.value,
        isError: asyncStateInstance.isError.value,
        data: asyncStateInstance.data.value,
        error: asyncStateInstance.error.value
      }
    }

    // 记录生命周期历史
    recordLifecycleEvent(eventData)

    // 执行钩子
    for (const hook of enabledHooks) {
      try {
        await hook.handler(eventData, hook.context)
        
        // 如果是一次性钩子，执行后移除
        if (hook.once) {
          offLifecycle(event, hook.id)
        }
      } catch (error) {
        console.error(`[AsyncLifecycle] Hook execution error for ${event}:`, error)
        
        // 触发钩子错误事件
        if (finalConfig.enableGlobalIntegration) {
          appStore.setError(error, {
            type: 'lifecycle_hook_error',
            event,
            hookId: hook.id
          })
        }
      }
    }

    if (finalConfig.enableLogging) {
      console.debug(`[AsyncLifecycle] Event emitted: ${event}`, { 
        hookCount: enabledHooks.length,
        data: eventData
      })
    }
  }

  // ==================== 便捷钩子方法 ====================

  /**
   * 执行前钩子
   */
  const beforeExecute = (handler, options) => 
    onLifecycle(LifecycleEvents.BEFORE_EXECUTE, handler, options)

  /**
   * 执行后钩子
   */
  const afterExecute = (handler, options) => 
    onLifecycle(LifecycleEvents.AFTER_EXECUTE, handler, options)

  /**
   * 成功钩子
   */
  const onSuccess = (handler, options) => 
    onLifecycle(LifecycleEvents.ON_SUCCESS, handler, options)

  /**
   * 错误钩子
   */
  const onError = (handler, options) => 
    onLifecycle(LifecycleEvents.ON_ERROR, handler, options)

  /**
   * 重试钩子
   */
  const onRetry = (handler, options) => 
    onLifecycle(LifecycleEvents.ON_RETRY, handler, options)

  /**
   * 取消钩子
   */
  const onCancel = (handler, options) => 
    onLifecycle(LifecycleEvents.ON_CANCEL, handler, options)

  /**
   * 重置钩子
   */
  const onReset = (handler, options) => 
    onLifecycle(LifecycleEvents.ON_RESET, handler, options)

  /**
   * 状态变化钩子
   */
  const onStateChange = (handler, options) => 
    onLifecycle(LifecycleEvents.STATE_CHANGE, handler, options)

  // ==================== 生命周期历史管理 ====================

  /**
   * 记录生命周期事件
   * @param {Object} eventData - 事件数据
   */
  const recordLifecycleEvent = (eventData) => {
    lifecycleHistory.value.push(eventData)

    // 限制历史记录大小
    if (lifecycleHistory.value.length > finalConfig.maxHistorySize) {
      lifecycleHistory.value = lifecycleHistory.value.slice(-finalConfig.maxHistorySize)
    }
  }

  /**
   * 获取生命周期历史
   * @param {string} event - 事件类型过滤
   * @param {number} limit - 限制数量
   * @returns {Array} 历史记录
   */
  const getLifecycleHistory = (event = null, limit = null) => {
    let history = lifecycleHistory.value

    if (event) {
      history = history.filter(item => item.event === event)
    }

    if (limit && limit > 0) {
      history = history.slice(-limit)
    }

    return history
  }

  /**
   * 清除生命周期历史
   */
  const clearLifecycleHistory = () => {
    lifecycleHistory.value = []
  }

  // ==================== 指标统计 ====================

  /**
   * 更新执行指标
   * @param {string} type - 指标类型
   * @param {Object} data - 指标数据
   */
  const updateMetrics = (type, data = {}) => {
    if (!finalConfig.enableMetrics) return

    switch (type) {
      case 'execution_start':
        executionStartTime.value = Date.now()
        metrics.value.totalExecutions++
        break

      case 'execution_end':
        executionEndTime.value = Date.now()
        if (executionStartTime.value) {
          const duration = executionEndTime.value - executionStartTime.value
          const totalTime = metrics.value.averageExecutionTime * (metrics.value.totalExecutions - 1) + duration
          metrics.value.averageExecutionTime = totalTime / metrics.value.totalExecutions
          metrics.value.lastExecutionTime = duration
        }
        break

      case 'success':
        metrics.value.successCount++
        break

      case 'error':
        metrics.value.errorCount++
        break

      case 'retry':
        metrics.value.retryCount++
        break
    }
  }

  /**
   * 获取性能指标
   * @returns {Object} 性能指标
   */
  const getMetrics = () => ({
    ...metrics.value,
    successRate: metrics.value.totalExecutions > 0 
      ? (metrics.value.successCount / metrics.value.totalExecutions) * 100 
      : 0,
    errorRate: metrics.value.totalExecutions > 0 
      ? (metrics.value.errorCount / metrics.value.totalExecutions) * 100 
      : 0,
    retryRate: metrics.value.totalExecutions > 0 
      ? (metrics.value.retryCount / metrics.value.totalExecutions) * 100 
      : 0
  })

  /**
   * 重置指标
   */
  const resetMetrics = () => {
    metrics.value = {
      totalExecutions: 0,
      successCount: 0,
      errorCount: 0,
      retryCount: 0,
      averageExecutionTime: 0,
      lastExecutionTime: null
    }
    executionStartTime.value = null
    executionEndTime.value = null
  }

  // ==================== 状态监听和集成 ====================

  // 监听异步状态变化
  watch(
    () => asyncStateInstance.state.value,
    async (newState, oldState) => {
      if (newState === oldState) return

      // 触发状态变化事件
      await emitLifecycle(LifecycleEvents.STATE_CHANGE, {
        from: oldState,
        to: newState,
        timestamp: Date.now()
      })

      // 根据状态触发相应的生命周期事件
      switch (newState) {
        case AsyncStates.LOADING:
          if (oldState === AsyncStates.IDLE) {
            updateMetrics('execution_start')
            await emitLifecycle(LifecycleEvents.BEFORE_EXECUTE)
          }
          break

        case AsyncStates.SUCCESS:
          updateMetrics('execution_end')
          updateMetrics('success')
          await emitLifecycle(LifecycleEvents.ON_SUCCESS, {
            data: asyncStateInstance.data.value
          })
          await emitLifecycle(LifecycleEvents.AFTER_EXECUTE, {
            success: true,
            data: asyncStateInstance.data.value
          })
          break

        case AsyncStates.ERROR:
          updateMetrics('execution_end')
          updateMetrics('error')
          await emitLifecycle(LifecycleEvents.ON_ERROR, {
            error: asyncStateInstance.error.value
          })
          await emitLifecycle(LifecycleEvents.AFTER_EXECUTE, {
            success: false,
            error: asyncStateInstance.error.value
          })
          break

        case AsyncStates.RETRYING:
          updateMetrics('retry')
          await emitLifecycle(LifecycleEvents.ON_RETRY, {
            attempt: asyncStateInstance.retryAttempts.value,
            error: asyncStateInstance.error.value
          })
          break

        case AsyncStates.IDLE:
          if (oldState !== AsyncStates.IDLE) {
            await emitLifecycle(LifecycleEvents.ON_RESET)
          }
          break
      }
    },
    { immediate: false }
  )

  // ==================== 清理和重置 ====================

  /**
   * 清理所有钩子和状态
   */
  const cleanup = () => {
    activeHooks.value.clear()
    clearLifecycleHistory()
    resetMetrics()
  }

  // 组件卸载时自动清理
  if (finalConfig.autoCleanup) {
    onUnmounted(() => {
      cleanup()
    })
  }

  // ==================== 返回接口 ====================

  return {
    // 钩子注册
    onLifecycle,
    offLifecycle,
    emitLifecycle,

    // 便捷钩子方法
    beforeExecute,
    afterExecute,
    onSuccess,
    onError,
    onRetry,
    onCancel,
    onReset,
    onStateChange,

    // 历史管理
    lifecycleHistory,
    getLifecycleHistory,
    clearLifecycleHistory,

    // 指标统计
    metrics,
    getMetrics,
    resetMetrics,
    updateMetrics,

    // 管理方法
    cleanup,
    
    // 状态信息
    activeHooks
  }
}
