<template>
  <div class="rule-dashboard-debug">
    <h1>调试仪表盘</h1>
    
    <!-- 基础状态显示 -->
    <div class="debug-info">
      <h3>调试信息</h3>
      <p>Loading: {{ loading }}</p>
      <p>Rules count: {{ rulesCount }}</p>
      <p>Rules data: {{ rules ? 'exists' : 'null' }}</p>
      <p>Status counts: {{ statusCounts ? 'exists' : 'null' }}</p>
    </div>

    <!-- 错误捕获 -->
    <div class="error-boundary">
      <h3>组件测试</h3>
      <div v-if="loading">加载中...</div>
      <div v-else-if="error">错误: {{ error }}</div>
      <div v-else>
        <p>规则数量: {{ safeRulesCount }}</p>
        <div v-if="safeStatusCounts">
          <h4>状态统计:</h4>
          <ul>
            <li v-for="(count, status) in safeStatusCounts" :key="status">
              {{ status }}: {{ count }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button @click="testRefresh" :loading="loading">刷新数据</el-button>
      <el-button @click="testStore">测试Store</el-button>
      <el-button @click="clearError">清除错误</el-button>
    </div>

    <!-- 原始数据显示 -->
    <div class="raw-data" v-if="showRawData">
      <h3>原始数据</h3>
      <pre>{{ JSON.stringify({ rules, statusCounts, loading }, null, 2) }}</pre>
    </div>
    
    <el-button @click="showRawData = !showRawData">
      {{ showRawData ? '隐藏' : '显示' }}原始数据
    </el-button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onErrorCaptured } from 'vue'
import { useRulesStore } from '../stores/rules'
import { ElMessage } from 'element-plus'

// Store
const rulesStore = useRulesStore()

// 本地状态
const error = ref(null)
const showRawData = ref(false)

// Store状态 - 使用安全访问
const rules = computed(() => {
  try {
    return rulesStore.rules || []
  } catch (e) {
    error.value = `Rules访问错误: ${e.message}`
    return []
  }
})

const loading = computed(() => {
  try {
    return rulesStore.loading || false
  } catch (e) {
    error.value = `Loading访问错误: ${e.message}`
    return false
  }
})

const statusCounts = computed(() => {
  try {
    return rulesStore.statusCounts || {}
  } catch (e) {
    error.value = `StatusCounts访问错误: ${e.message}`
    return {}
  }
})

const rulesCount = computed(() => {
  try {
    return rulesStore.rulesCount || 0
  } catch (e) {
    error.value = `RulesCount访问错误: ${e.message}`
    return 0
  }
})

// 安全的计算属性
const safeRulesCount = computed(() => {
  try {
    const rulesData = rules.value
    return Array.isArray(rulesData) ? rulesData.length : 0
  } catch (e) {
    error.value = `SafeRulesCount错误: ${e.message}`
    return 0
  }
})

const safeStatusCounts = computed(() => {
  try {
    const counts = statusCounts.value
    return counts && typeof counts === 'object' ? counts : {}
  } catch (e) {
    error.value = `SafeStatusCounts错误: ${e.message}`
    return {}
  }
})

// 方法
const testRefresh = async () => {
  try {
    error.value = null
    console.log('开始刷新数据...')
    await rulesStore.fetchRules(true)
    console.log('刷新完成')
    ElMessage.success('刷新成功')
  } catch (e) {
    error.value = `刷新错误: ${e.message}`
    console.error('刷新失败:', e)
    ElMessage.error('刷新失败')
  }
}

const testStore = () => {
  try {
    error.value = null
    console.log('Store状态:', {
      rules: rulesStore.rules,
      loading: rulesStore.loading,
      statusCounts: rulesStore.statusCounts,
      rulesCount: rulesStore.rulesCount
    })
    ElMessage.info('Store状态已输出到控制台')
  } catch (e) {
    error.value = `Store测试错误: ${e.message}`
    console.error('Store测试失败:', e)
  }
}

const clearError = () => {
  error.value = null
}

// 错误捕获
onErrorCaptured((err, instance, info) => {
  console.error('组件错误捕获:', err, info)
  error.value = `组件错误: ${err.message}`
  return false
})

// 生命周期
onMounted(async () => {
  try {
    console.log('调试仪表盘挂载')
    await testRefresh()
  } catch (e) {
    error.value = `挂载错误: ${e.message}`
    console.error('挂载失败:', e)
  }
})
</script>

<style scoped>
.rule-dashboard-debug {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.debug-info, .error-boundary, .actions, .raw-data {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.debug-info {
  background-color: #f0f9ff;
}

.error-boundary {
  background-color: #fef2f2;
}

.actions {
  background-color: #f9fafb;
}

.raw-data {
  background-color: #f3f4f6;
}

.raw-data pre {
  white-space: pre-wrap;
  word-break: break-word;
}

.actions .el-button {
  margin-right: 10px;
}
</style>
