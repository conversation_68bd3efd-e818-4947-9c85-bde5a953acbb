<template>
  <div class="rule-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">规则配置管理</h1>
        <p class="page-description">管理和配置规则模板，设置规则的基本信息、验证逻辑和参数</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="handleCreateRule">
          新建规则
        </el-button>
        <el-button :icon="Refresh" @click="handleRefresh" :loading="isLoading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 功能说明卡片 -->
    <div class="feature-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="feature-card">
            <template #header>
              <div class="card-header">
                <el-icon class="feature-icon"><Setting /></el-icon>
                <span>规则模板配置</span>
              </div>
            </template>
            <p>创建和编辑规则模板，定义规则的基本信息、分类和描述</p>
            <ul>
              <li>规则名称和描述</li>
              <li>规则类别和优先级</li>
              <li>适用范围和条件</li>
            </ul>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="feature-card">
            <template #header>
              <div class="card-header">
                <el-icon class="feature-icon"><Tools /></el-icon>
                <span>验证逻辑设置</span>
              </div>
            </template>
            <p>配置规则的验证逻辑和参数，定义数据检查规则</p>
            <ul>
              <li>字段验证规则</li>
              <li>业务逻辑检查</li>
              <li>错误级别定义</li>
            </ul>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="feature-card">
            <template #header>
              <div class="card-header">
                <el-icon class="feature-icon"><Monitor /></el-icon>
                <span>规则状态监控</span>
              </div>
            </template>
            <p>监控规则的运行状态和执行效果，查看统计信息</p>
            <ul>
              <li>规则执行统计</li>
              <li>错误率监控</li>
              <li>性能指标分析</li>
            </ul>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 规则列表 -->
    <div class="rule-list-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>规则模板列表</h3>
            <div class="header-tools">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索规则名称..."
                style="width: 300px; margin-right: 12px;"
                clearable
                :prefix-icon="Search"
              />
              <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
                <el-option label="全部" value="" />
                <el-option label="活跃" value="ACTIVE" />
                <el-option label="停用" value="INACTIVE" />
              </el-select>
            </div>
          </div>
        </template>

        <!-- 错误状态显示 -->
        <div v-if="hasError" style="margin-bottom: 16px;">
          <el-alert
            :title="currentError?.message || '系统错误'"
            type="error"
            :description="currentError?.details?.error"
            show-icon
            :closable="true"
            @close="appStore.clearError()"
          />
        </div>

        <el-table :data="filteredRules" v-loading="isLoading" stripe>
          <el-table-column prop="rule_key" label="规则键" width="150" />
          <el-table-column prop="rule_name" label="规则名称" min-width="200" />
          <el-table-column prop="rule_category" label="规则类别" width="120" />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <StatusTag :status="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160" />
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <el-button size="small" type="primary" @click="handleViewRule(scope.row)">
                  查看
                </el-button>
                <el-button size="small" type="success" @click="handleEditRule(scope.row)">
                  编辑
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  :icon="List"
                  @click="handleManageDetails(scope.row)"
                  :loading="detailsCountLoading[scope.row.rule_key]"
                >
                  {{ getDetailsButtonText(scope.row.rule_key) }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-wrapper">
          <el-pagination
            :current-page="pagination.page"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handlePageSizeChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <h3>快速操作</h3>
        </template>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-button type="primary" block @click="handleBatchImport">
              批量导入规则
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="success" block @click="handleExportRules">
              导出规则配置
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="warning" block @click="handleValidateRules">
              规则验证检查
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="info" block @click="handleViewStatistics">
              查看统计报告
            </el-button>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Refresh, Search, Setting, Tools, Monitor, List } from '@element-plus/icons-vue'
import { getRulesStatus } from '../api/rules'
import { getRuleDetailsList } from '../api/ruleDetails'
import StatusTag from '../components/common/StatusTag.vue'
import { useAppStore } from '../stores/app'
import { storeToRefs } from 'pinia'

const router = useRouter()

// 全局状态管理
const appStore = useAppStore()
const { globalLoading, hasError, currentError } = storeToRefs(appStore)

// 响应式数据
const loading = ref(false)

// 明细数量相关状态
const detailsCounts = ref({}) // 存储每个规则的明细数量
const detailsCountLoading = ref({}) // 存储每个规则的加载状态

// 统一加载状态
const isLoading = computed(() => loading.value || globalLoading.value)

// 统一错误处理
const handleError = (error, message = '操作失败') => {
  console.error(message, error)
  ElMessage.error(`${message}: ${error.message || '请稍后重试'}`)

  // 设置全局错误状态
  appStore.setError({
    type: 'OPERATION_ERROR',
    message: message,
    details: { error: error.message || error }
  })
}
const searchKeyword = ref('')
const statusFilter = ref('')
const allRules = ref([]) // 存储所有规则数据
const rules = ref([]) // 存储当前页显示的规则数据
const pagination = ref({
  page: 1,
  pageSize: 20,
  total: 0
})

// 移除不必要的状态映射，直接使用后端原始状态，与规则仪表盘保持一致

// 计算属性 - 过滤和分页处理
const filteredRules = computed(() => {
  let filtered = allRules.value

  // 搜索过滤
  if (searchKeyword.value && searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase()
    filtered = filtered.filter(rule =>
      rule.rule_name?.toLowerCase().includes(keyword) ||
      rule.rule_key?.toLowerCase().includes(keyword)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(rule => rule.status === statusFilter.value)
  }

  // 更新总数
  pagination.value.total = filtered.length

  // 分页处理
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize

  return filtered.slice(start, end)
})

// 监听过滤条件变化，重置到第一页
watch([searchKeyword, statusFilter], () => {
  pagination.value.page = 1
})

// 监听计算属性变化，更新显示的规则列表
watch(filteredRules, (newRules) => {
  rules.value = newRules
}, { immediate: true })

// 方法
const handleRefresh = async () => {
  loading.value = true
  try {
    console.log('调用API获取规则状态列表...')

    const response = await getRulesStatus()

    if (response && response.success) {
      // 转换API响应数据格式，直接使用后端状态，与规则仪表盘保持一致
      allRules.value = (response.data || []).map(rule => ({
        rule_key: rule.rule_key,
        rule_name: rule.rule_name || rule.rule_key,
        rule_category: rule.rule_category || '未分类',
        status: rule.status || 'UNKNOWN', // 直接使用后端状态
        created_at: rule.created_at || rule.updated_at || new Date().toISOString()
      }))

      console.log('获取规则列表成功:', {
        count: allRules.value.length,
        statuses: [...new Set(allRules.value.map(rule => rule.status))], // 显示所有状态类型
        rules: allRules.value.slice(0, 3) // 只显示前3条用于调试
      })

      ElMessage.success(`规则列表刷新成功，共 ${allRules.value.length} 条规则`)

      // 异步获取明细数量
      loadDetailsCounts()
    } else {
      throw new Error(response?.message || '获取规则列表失败')
    }
  } catch (error) {
    handleError(error, '获取规则列表失败')

    // 失败时使用空数组
    allRules.value = []
    rules.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

const handleCreateRule = () => {
  ElMessage.info('新建规则功能开发中...')
}

const handleViewRule = (rule) => {
  router.push(`/rule-template-detail/${rule.rule_key}`)
}

const handleEditRule = (rule) => {
  ElMessage.info(`编辑规则: ${rule.rule_name}`)
}

// 批量加载明细数量
const loadDetailsCounts = async () => {
  const rules = allRules.value || []

  // 并发获取明细数量，但限制并发数避免过多请求
  const batchSize = 5
  for (let i = 0; i < rules.length; i += batchSize) {
    const batch = rules.slice(i, i + batchSize)
    await Promise.all(
      batch.map(rule => fetchDetailsCount(rule.rule_key))
    )
  }
}

// 获取规则明细数量
const fetchDetailsCount = async (ruleKey) => {
  if (detailsCounts.value[ruleKey] !== undefined) {
    return detailsCounts.value[ruleKey] // 已缓存，直接返回
  }

  detailsCountLoading.value[ruleKey] = true
  try {
    const response = await getRuleDetailsList(ruleKey, { page: 1, page_size: 1 })
    const count = response?.data?.total || 0
    detailsCounts.value[ruleKey] = count
    return count
  } catch (error) {
    console.error(`获取规则 ${ruleKey} 明细数量失败:`, error)
    detailsCounts.value[ruleKey] = 0
    return 0
  } finally {
    detailsCountLoading.value[ruleKey] = false
  }
}

// 获取按钮显示文本
const getDetailsButtonText = (ruleKey) => {
  if (detailsCountLoading.value[ruleKey]) {
    return '加载中...'
  }

  const count = detailsCounts.value[ruleKey]
  if (count === undefined) {
    return '管理明细'
  }

  return `管理明细(${count})`
}

// 管理规则明细
const handleManageDetails = (rule) => {
  router.push(`/rules/${rule.rule_key}/details`)
}

const handleViewDetails = (rule) => {
  // 跳转到规则模板详情页面，因为规则明细界面已被移除
  router.push(`/rule-template-detail/${rule.rule_key}`)
}

const handlePageChange = (page) => {
  pagination.value.page = page
  console.log('页码变更:', page)
}

const handlePageSizeChange = (size) => {
  pagination.value.pageSize = size
  pagination.value.page = 1
  console.log('页面大小变更:', size)
}

const handleBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

const handleExportRules = () => {
  ElMessage.info('导出功能开发中...')
}

const handleValidateRules = () => {
  ElMessage.info('规则验证功能开发中...')
}

const handleViewStatistics = () => {
  ElMessage.info('统计报告功能开发中...')
}

// 组件挂载时获取数据
onMounted(() => {
  handleRefresh()
})
</script>

<style scoped>
.rule-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.feature-cards {
  margin-bottom: 24px;
}

.feature-card {
  height: 100%;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.feature-icon {
  color: #409eff;
  font-size: 18px;
}

.feature-card p {
  color: #606266;
  margin-bottom: 12px;
}

.feature-card ul {
  margin: 0;
  padding-left: 16px;
  color: #909399;
}

.feature-card li {
  margin-bottom: 4px;
  font-size: 13px;
}

.rule-list-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  color: #303133;
}

.header-tools {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.quick-actions {
  margin-bottom: 24px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rule-management {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .header-tools {
    width: 100%;
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .action-buttons .el-button {
    width: 100%;
    margin: 0;
  }
}
</style>
