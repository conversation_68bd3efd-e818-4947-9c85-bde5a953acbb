<template>
  <teleport to="body">
    <transition name="r-modal" appear>
      <div v-if="visible" :class="modalClasses" @click="handleMaskClick">
        <div :class="dialogClasses" :style="dialogStyles" @click.stop>
          <!-- 模态框头部 -->
          <div v-if="showHeader" class="r-modal__header">
            <slot name="header">
              <h3 class="r-modal__title">{{ title }}</h3>
            </slot>
            
            <!-- 关闭按钮 -->
            <button
              v-if="showClose"
              class="r-modal__close"
              @click="handleClose"
            >
              <el-icon>
                <Close />
              </el-icon>
            </button>
          </div>
          
          <!-- 模态框主体 -->
          <div class="r-modal__body">
            <slot />
          </div>
          
          <!-- 模态框底部 -->
          <div v-if="$slots.footer || showDefaultFooter" class="r-modal__footer">
            <slot name="footer">
              <div v-if="showDefaultFooter" class="r-modal__actions">
                <r-button
                  variant="secondary"
                  @click="handleCancel"
                >
                  {{ cancelText }}
                </r-button>
                <r-button
                  variant="primary"
                  :loading="confirmLoading"
                  @click="handleConfirm"
                >
                  {{ confirmText }}
                </r-button>
              </div>
            </slot>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { computed, watch, nextTick } from 'vue'
import { ElIcon } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { designTokens } from '../../tokens'
import RButton from './RButton.vue'

const props = defineProps({
  // 是否显示
  visible: {
    type: Boolean,
    default: false
  },
  
  // 模态框标题
  title: {
    type: String,
    default: ''
  },
  
  // 模态框宽度
  width: {
    type: [String, Number],
    default: '50%'
  },
  
  // 模态框高度
  height: {
    type: [String, Number],
    default: 'auto'
  },
  
  // 模态框尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl', 'full'].includes(value)
  },
  
  // 是否显示头部
  showHeader: {
    type: Boolean,
    default: true
  },
  
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: true
  },
  
  // 是否显示默认底部
  showDefaultFooter: {
    type: Boolean,
    default: false
  },
  
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确认'
  },
  
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消'
  },
  
  // 确认按钮加载状态
  confirmLoading: {
    type: Boolean,
    default: false
  },
  
  // 点击遮罩是否关闭
  maskClosable: {
    type: Boolean,
    default: true
  },
  
  // 按ESC是否关闭
  keyboard: {
    type: Boolean,
    default: true
  },
  
  // 是否居中
  centered: {
    type: Boolean,
    default: false
  },
  
  // 是否全屏
  fullscreen: {
    type: Boolean,
    default: false
  },
  
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  },
  
  // z-index
  zIndex: {
    type: Number,
    default: 1000
  }
})

const emit = defineEmits(['update:visible', 'confirm', 'cancel', 'close'])

// 计算属性
const modalClasses = computed(() => [
  'r-modal',
  {
    'r-modal--centered': props.centered,
    'r-modal--fullscreen': props.fullscreen
  },
  props.customClass
])

const dialogClasses = computed(() => [
  'r-modal__dialog',
  `r-modal__dialog--${props.size}`,
  {
    'r-modal__dialog--fullscreen': props.fullscreen
  }
])

const dialogStyles = computed(() => {
  const styles = {
    zIndex: props.zIndex
  }
  
  if (!props.fullscreen) {
    if (props.width) {
      styles.width = typeof props.width === 'number' ? `${props.width}px` : props.width
    }
    
    if (props.height && props.height !== 'auto') {
      styles.height = typeof props.height === 'number' ? `${props.height}px` : props.height
    }
  }
  
  return styles
})

// 方法
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  handleClose()
}

const handleMaskClick = () => {
  if (props.maskClosable) {
    handleClose()
  }
}

const handleKeydown = (event) => {
  if (props.keyboard && event.key === 'Escape') {
    handleClose()
  }
}

// 监听键盘事件
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      document.addEventListener('keydown', handleKeydown)
      document.body.style.overflow = 'hidden'
    })
  } else {
    document.removeEventListener('keydown', handleKeydown)
    document.body.style.overflow = ''
  }
})
</script>

<style scoped>
.r-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: v-bind('props.zIndex');
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: v-bind('designTokens.spacing.spacing[6]');
  background-color: v-bind('designTokens.colors.background.overlay');
  overflow-y: auto;
}

.r-modal--centered {
  align-items: center;
}

.r-modal--fullscreen {
  padding: 0;
}

.r-modal__dialog {
  position: relative;
  background-color: v-bind('designTokens.colors.background.paper');
  border-radius: v-bind('designTokens.borderRadius.lg');
  box-shadow: v-bind('designTokens.shadows["2xl"]');
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.r-modal__dialog--fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  max-width: none;
  max-height: none;
  border-radius: 0;
}

/* 尺寸变体 */
.r-modal__dialog--xs {
  width: 320px;
}

.r-modal__dialog--sm {
  width: 480px;
}

.r-modal__dialog--md {
  width: 640px;
}

.r-modal__dialog--lg {
  width: 800px;
}

.r-modal__dialog--xl {
  width: 1024px;
}

/* 头部 */
.r-modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: v-bind('designTokens.spacing.spacing[6]');
  border-bottom: 1px solid v-bind('designTokens.colors.border.light');
}

.r-modal__title {
  margin: 0;
  font-size: v-bind('designTokens.typography.fontSize.lg');
  font-weight: v-bind('designTokens.typography.fontWeight.semibold');
  color: v-bind('designTokens.colors.text.primary');
}

.r-modal__close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: v-bind('designTokens.spacing.spacing[8]');
  height: v-bind('designTokens.spacing.spacing[8]');
  border: none;
  background: none;
  border-radius: v-bind('designTokens.borderRadius.base');
  color: v-bind('designTokens.colors.text.tertiary');
  cursor: pointer;
  transition: v-bind('designTokens.transitions.presets.fast');
}

.r-modal__close:hover {
  background-color: v-bind('designTokens.colors.background.neutral');
  color: v-bind('designTokens.colors.text.primary');
}

/* 主体 */
.r-modal__body {
  flex: 1;
  padding: v-bind('designTokens.spacing.spacing[6]');
  overflow-y: auto;
  color: v-bind('designTokens.colors.text.secondary');
  line-height: v-bind('designTokens.typography.lineHeight.relaxed');
}

/* 底部 */
.r-modal__footer {
  padding: v-bind('designTokens.spacing.spacing[6]');
  border-top: 1px solid v-bind('designTokens.colors.border.light');
}

.r-modal__actions {
  display: flex;
  justify-content: flex-end;
  gap: v-bind('designTokens.spacing.spacing[3]');
}

/* 动画 */
.r-modal-enter-active,
.r-modal-leave-active {
  transition: opacity 0.3s ease;
}

.r-modal-enter-active .r-modal__dialog,
.r-modal-leave-active .r-modal__dialog {
  transition: transform 0.3s ease;
}

.r-modal-enter-from,
.r-modal-leave-to {
  opacity: 0;
}

.r-modal-enter-from .r-modal__dialog,
.r-modal-leave-to .r-modal__dialog {
  transform: scale(0.9) translateY(-20px);
}

/* 响应式 */
@media (max-width: 640px) {
  .r-modal {
    padding: v-bind('designTokens.spacing.spacing[4]');
  }
  
  .r-modal__dialog {
    width: 100% !important;
    max-width: none;
  }
  
  .r-modal__header,
  .r-modal__body,
  .r-modal__footer {
    padding: v-bind('designTokens.spacing.spacing[4]');
  }
  
  .r-modal__actions {
    flex-direction: column;
  }
}
</style>
