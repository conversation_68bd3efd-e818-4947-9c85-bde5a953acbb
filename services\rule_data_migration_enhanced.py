"""
增强的规则数据迁移服务
负责将 rule_data_sets 表中的 JSON 数据迁移到新的 rule_details 表
包含完整的数据验证、进度监控、错误处理和回滚机制
"""

from collections.abc import Callable
from datetime import datetime
from decimal import Decimal, InvalidOperation
from typing import Any

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import MigrationStatusEnum, RuleDataSet, RuleDetail, RuleDetailStatusEnum


class MigrationValidationError(Exception):
    """迁移验证相关异常"""

    def __init__(
            self,
            message: str,
            dataset_id: int | None = None, 
            validation_errors: list[str] | None = None
        ):
        super().__init__(message)
        self.dataset_id = dataset_id
        self.validation_errors = validation_errors or []


class EnhancedRuleDataMigration:
    """增强的规则数据迁移服务"""

    def __init__(
        self,
        session_factory,
        batch_size: int = 100,
        enable_validation: bool = True,
        progress_callback: Callable | None = None,
    ):
        """
        初始化增强的迁移服务

        Args:
            session_factory: 数据库会话工厂
            batch_size: 批量处理大小
            enable_validation: 是否启用数据验证
            progress_callback: 进度回调函数
        """
        self.session_factory = session_factory
        self.batch_size = batch_size
        self.enable_validation = enable_validation
        self.progress_callback = progress_callback

        # 迁移统计信息
        self.migration_stats = {
            "total_datasets": 0,
            "processed_datasets": 0,
            "successful_datasets": 0,
            "failed_datasets": 0,
            "skipped_datasets": 0,
            "total_details": 0,
            "successful_details": 0,
            "failed_details": 0,
            "validation_errors": 0,
            "start_time": None,
            "end_time": None,
            "duration_seconds": 0,
            "errors": [],
            "warnings": [],
            "validation_issues": []
        }

        logger.info(
            f"EnhancedRuleDataMigration 初始化完成，"
            f"批量大小: {batch_size}, 验证: {enable_validation}"
        )

    def migrate_all_datasets(
            self,
            dry_run: bool = False, 
            continue_on_error: bool = True
        ) -> dict[str, Any]:
        """
        迁移所有待迁移的数据集

        Args:
            dry_run: 是否为试运行模式（不实际写入数据）
            continue_on_error: 遇到错误时是否继续处理其他数据集

        Returns:
            Dict: 迁移结果统计
        """
        logger.info(f"开始迁移所有数据集... (试运行: {dry_run}, 遇错继续: {continue_on_error})")

        # 重置统计信息
        self._reset_stats()

        try:
            with self.session_factory() as session:
                # 获取所有待迁移的数据集
                datasets = self._get_pending_datasets(session)
                self.migration_stats["total_datasets"] = len(datasets)

                logger.info(f"找到 {len(datasets)} 个待迁移的数据集")

                if len(datasets) == 0:
                    logger.info("没有待迁移的数据集")
                    return self.migration_stats.copy()

                # 分批处理数据集
                for i, dataset in enumerate(datasets):
                    try:
                        self._process_single_dataset(session, dataset, dry_run, i + 1)

                        if not dry_run:
                            session.commit()

                    except Exception as e:
                        error_msg = f"数据集 {dataset.id} 处理失败: {str(e)}"
                        self.migration_stats["errors"].append(error_msg)
                        logger.error(error_msg, exc_info=True)

                        if not continue_on_error:
                            raise

                        # 回滚当前事务
                        if not dry_run:
                            session.rollback()

                self._finalize_migration()
                return self.migration_stats.copy()

        except Exception as e:
            logger.error(f"数据迁移过程中发生严重错误: {e}", exc_info=True)
            self.migration_stats["errors"].append(f"严重错误: {str(e)}")
            self._finalize_migration()
            raise

    def migrate_single_dataset_by_id(
            self,
            dataset_id: int, 
            dry_run: bool = False
        ) -> dict[str, Any]:
        """
        迁移指定ID的数据集

        Args:
            dataset_id: 数据集ID
            dry_run: 是否为试运行模式

        Returns:
            Dict: 迁移结果
        """
        logger.info(f"开始迁移数据集 {dataset_id}... (试运行: {dry_run})")

        self._reset_stats()

        try:
            with self.session_factory() as session:
                dataset = session.query(RuleDataSet).filter(
                    RuleDataSet.id == dataset_id
                ).first()

                if not dataset:
                    raise ValueError(f"数据集 {dataset_id} 不存在")

                if dataset.migration_status == MigrationStatusEnum.COMPLETED:
                    logger.warning(f"数据集 {dataset_id} 已经迁移完成")
                    self.migration_stats["skipped_datasets"] = 1
                    return self.migration_stats.copy()

                self.migration_stats["total_datasets"] = 1
                self._process_single_dataset(session, dataset, dry_run, 1)

                if not dry_run:
                    session.commit()

                self._finalize_migration()
                return self.migration_stats.copy()

        except Exception as e:
            logger.error(f"迁移数据集 {dataset_id} 失败: {e}", exc_info=True)
            self.migration_stats["errors"].append(f"数据集 {dataset_id} 迁移失败: {str(e)}")
            self._finalize_migration()
            raise

    def validate_migration_integrity(self) -> dict[str, Any]:
        """
        验证迁移后的数据完整性

        Returns:
            Dict: 验证结果
        """
        logger.info("开始验证迁移数据完整性...")

        validation_result = {
            "total_datasets": 0,
            "validated_datasets": 0,
            "integrity_issues": [],
            "missing_details": [],
            "extra_details": [],
            "data_mismatches": [],
            "validation_passed": True
        }

        try:
            with self.session_factory() as session:
                # 获取所有已完成迁移的数据集
                completed_datasets = session.query(RuleDataSet).filter(
                    RuleDataSet.migration_status == MigrationStatusEnum.COMPLETED
                ).all()

                validation_result["total_datasets"] = len(completed_datasets)

                for dataset in completed_datasets:
                    try:
                        dataset_validation = self._validate_single_dataset(session, dataset)
                        validation_result["validated_datasets"] += 1

                        # 合并验证结果
                        for key in ["integrity_issues", "missing_details", "extra_details", "data_mismatches"]:
                            validation_result[key].extend(dataset_validation.get(key, []))

                        if not dataset_validation.get("passed", True):
                            validation_result["validation_passed"] = False

                    except Exception as e:
                        error_msg = f"验证数据集 {dataset.id} 失败: {str(e)}"
                        validation_result["integrity_issues"].append(error_msg)
                        validation_result["validation_passed"] = False
                        logger.error(error_msg, exc_info=True)

                logger.info(f"数据完整性验证完成，验证了 {validation_result['validated_datasets']} 个数据集")

                if validation_result["validation_passed"]:
                    logger.info("✅ 数据完整性验证通过")
                else:
                    logger.warning("⚠️ 数据完整性验证发现问题")

                return validation_result

        except Exception as e:
            logger.error(f"数据完整性验证失败: {e}", exc_info=True)
            validation_result["integrity_issues"].append(f"验证过程失败: {str(e)}")
            validation_result["validation_passed"] = False
            return validation_result

    def rollback_migration(self, dataset_ids: list[int] | None = None) -> dict[str, Any]:
        """
        回滚迁移操作

        Args:
            dataset_ids: 要回滚的数据集ID列表，None表示回滚所有

        Returns:
            Dict: 回滚结果
        """
        logger.info(f"开始回滚迁移操作... (数据集: {dataset_ids or '全部'})")

        rollback_result = {
            "total_datasets": 0,
            "rollback_datasets": 0,
            "failed_rollbacks": 0,
            "deleted_details": 0,
            "errors": []
        }

        try:
            with self.session_factory() as session:
                # 确定要回滚的数据集
                if dataset_ids:
                    datasets = session.query(RuleDataSet).filter(
                        RuleDataSet.id.in_(dataset_ids)
                    ).all()
                else:
                    datasets = session.query(RuleDataSet).filter(
                        RuleDataSet.migration_status.in_([
                            MigrationStatusEnum.COMPLETED,
                            MigrationStatusEnum.FAILED
                        ])
                    ).all()

                rollback_result["total_datasets"] = len(datasets)

                for dataset in datasets:
                    try:
                        # 删除迁移的明细数据
                        deleted_count = session.query(RuleDetail).filter(
                            RuleDetail.dataset_id == dataset.id
                        ).delete()

                        # 重置迁移状态
                        dataset.migration_status = MigrationStatusEnum.PENDING
                        dataset.migration_timestamp = None

                        rollback_result["rollback_datasets"] += 1
                        rollback_result["deleted_details"] += deleted_count

                        logger.info(f"数据集 {dataset.id} 回滚成功，删除了 {deleted_count} 条明细")

                    except Exception as e:
                        error_msg = f"回滚数据集 {dataset.id} 失败: {str(e)}"
                        rollback_result["errors"].append(error_msg)
                        rollback_result["failed_rollbacks"] += 1
                        logger.error(error_msg, exc_info=True)

                session.commit()

                logger.info(
                    f"回滚操作完成，成功: {rollback_result['rollback_datasets']}, "
                    f"失败: {rollback_result['failed_rollbacks']}"
                )

                return rollback_result

        except Exception as e:
            logger.error(f"回滚迁移操作失败: {e}", exc_info=True)
            rollback_result["errors"].append(f"回滚操作失败: {str(e)}")
            return rollback_result

    def _reset_stats(self):
        """重置统计信息"""
        self.migration_stats.update({
            "total_datasets": 0,
            "processed_datasets": 0,
            "successful_datasets": 0,
            "failed_datasets": 0,
            "skipped_datasets": 0,
            "total_details": 0,
            "successful_details": 0,
            "failed_details": 0,
            "validation_errors": 0,
            "start_time": datetime.now(),
            "end_time": None,
            "duration_seconds": 0,
            "errors": [],
            "warnings": [],
            "validation_issues": []
        })

    def _get_pending_datasets(self, session: Session) -> list[RuleDataSet]:
        """获取待迁移的数据集"""
        return session.query(RuleDataSet).filter(
            RuleDataSet.migration_status == MigrationStatusEnum.PENDING
        ).order_by(RuleDataSet.id).all()

    def _process_single_dataset(
            self,
            session: Session,
            dataset: RuleDataSet,
            dry_run: bool,
            sequence: int
        ):
        """处理单个数据集"""
        logger.info(f"处理数据集 {dataset.id} ({sequence}/{self.migration_stats['total_datasets']})")

        self.migration_stats["processed_datasets"] += 1

        # 预验证
        if self.enable_validation:
            validation_result = self._pre_validate_dataset(dataset)
            if not validation_result["valid"]:
                self.migration_stats["validation_errors"] += 1
                self.migration_stats["failed_datasets"] += 1
                error_msg = f"数据集 {dataset.id} 预验证失败: {validation_result['errors']}"
                self.migration_stats["validation_issues"].extend(validation_result["errors"])
                raise MigrationValidationError(error_msg, dataset.id, validation_result["errors"])

        # 执行迁移
        if not dry_run:
            dataset.migration_status = MigrationStatusEnum.IN_PROGRESS
            session.flush()

        try:
            detail_count = self._migrate_dataset_details(session, dataset, dry_run)

            if not dry_run:
                dataset.migration_status = MigrationStatusEnum.COMPLETED
                dataset.migration_timestamp = datetime.now()

            self.migration_stats["successful_datasets"] += 1
            self.migration_stats["total_details"] += detail_count
            self.migration_stats["successful_details"] += detail_count

            logger.info(f"数据集 {dataset.id} 迁移成功，迁移了 {detail_count} 条明细")

            # 进度回调
            if self.progress_callback:
                progress = (self.migration_stats["processed_datasets"] /
                          self.migration_stats["total_datasets"] * 100)
                self.progress_callback(progress, dataset.id, detail_count)

        except Exception:
            if not dry_run:
                dataset.migration_status = MigrationStatusEnum.FAILED
                dataset.migration_timestamp = datetime.now()

            self.migration_stats["failed_datasets"] += 1
            raise

    def _pre_validate_dataset(self, dataset: RuleDataSet) -> dict[str, Any]:
        """预验证数据集"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }

        # 检查数据集基本信息
        if not dataset.data_set:
            validation_result["valid"] = False
            validation_result["errors"].append("data_set 字段为空")
            return validation_result

        if not isinstance(dataset.data_set, list):
            validation_result["valid"] = False
            validation_result["errors"].append(f"data_set 不是列表格式: {type(dataset.data_set)}")
            return validation_result

        if len(dataset.data_set) == 0:
            validation_result["warnings"].append("data_set 为空列表")

        # 检查数据项格式
        for i, item in enumerate(dataset.data_set):
            if not isinstance(item, dict):
                validation_result["valid"] = False
                validation_result["errors"].append(f"第 {i} 项不是字典格式: {type(item)}")
                continue

            # 检查必填字段
            if not item.get("rule_id"):
                validation_result["errors"].append(f"第 {i} 项缺少 rule_id")

            if not item.get("rule_name"):
                validation_result["errors"].append(f"第 {i} 项缺少 rule_name")

        if validation_result["errors"]:
            validation_result["valid"] = False

        return validation_result

    def _migrate_dataset_details(self, session: Session, dataset: RuleDataSet,
                                dry_run: bool) -> int:
        """迁移数据集的明细数据"""
        if not dataset.data_set or not isinstance(dataset.data_set, list):
            return 0

        rule_details = []
        failed_items = 0

        for i, item in enumerate(dataset.data_set):
            if not isinstance(item, dict):
                failed_items += 1
                logger.warning(f"数据集 {dataset.id} 第 {i} 项不是字典格式，跳过")
                continue

            try:
                detail = self._create_rule_detail_from_json(dataset.id, item, dataset.uploaded_by)
                rule_details.append(detail)
            except Exception as e:
                failed_items += 1
                error_msg = f"数据集 {dataset.id} 第 {i} 项转换失败: {e}"
                self.migration_stats["warnings"].append(error_msg)
                logger.warning(error_msg)
                continue

        if rule_details and not dry_run:
            # 批量插入
            session.add_all(rule_details)
            session.flush()

        self.migration_stats["failed_details"] += failed_items

        logger.info(f"数据集 {dataset.id} 处理完成: 成功 {len(rule_details)}, 失败 {failed_items}")

        return len(rule_details)

    def _create_rule_detail_from_json(
            self,
            dataset_id: int,
            item: dict[str, Any],
            created_by: str | None
        ) -> RuleDetail:
        """从 JSON 数据创建 RuleDetail 对象"""

        detail = RuleDetail(
            dataset_id=dataset_id,
            rule_detail_id=item.get('rule_id'),
            rule_name=item.get('rule_name'),
            error_level_1=item.get('error_level_1'),
            error_level_2=item.get('error_level_2'),
            error_level_3=item.get('error_level_3'),
            error_reason=item.get('error_reason'),
            error_severity=item.get('error_severity'),
            quality_basis=item.get('quality_basis'),
            location_desc=item.get('location_desc'),
            prompt_field_type=item.get('prompt_field_type'),
            prompt_field_code=item.get('prompt_field_code'),
            prompt_field_seq=self._safe_int(item.get('prompt_field_seq')),
            rule_category=item.get('rule_category'),
            applicable_business=item.get('applicable_business'),
            applicable_region=item.get('applicable_region'),
            default_selected=self._safe_bool(item.get('default_selected')),
            involved_amount=self._safe_decimal(item.get('involved_amount')),
            usage_quantity=self._safe_int(item.get('usage_quantity')),
            violation_quantity=self._safe_int(item.get('violation_quantity')),
            usage_days=self._safe_int(item.get('usage_days')),
            violation_days=self._safe_int(item.get('violation_days')),
            violation_items=item.get('violation_items'),
            effective_start_time=self._safe_datetime(item.get('effective_start_time')),
            effective_end_time=self._safe_datetime(item.get('effective_end_time')),
            remark=item.get('remark'),
            status=RuleDetailStatusEnum.ACTIVE,
            created_by=created_by
        )

        return detail

    def _validate_single_dataset(self, session: Session, dataset: RuleDataSet) -> dict[str, Any]:
        """验证单个数据集的迁移完整性"""
        validation_result = {
            "dataset_id": dataset.id,
            "passed": True,
            "integrity_issues": [],
            "missing_details": [],
            "extra_details": [],
            "data_mismatches": []
        }

        try:
            # 获取原始 JSON 数据
            original_data = dataset.data_set or []
            if not isinstance(original_data, list):
                validation_result["passed"] = False
                validation_result["integrity_issues"].append("原始数据不是列表格式")
                return validation_result

            # 获取迁移后的明细数据
            migrated_details = session.query(RuleDetail).filter(
                RuleDetail.dataset_id == dataset.id,
                RuleDetail.status == RuleDetailStatusEnum.ACTIVE
            ).all()

            # 比较数量
            original_count = len([item for item in original_data if isinstance(item, dict) and item.get('rule_id')])
            migrated_count = len(migrated_details)

            if original_count != migrated_count:
                validation_result["passed"] = False
                validation_result["integrity_issues"].append(
                    f"数量不匹配: 原始 {original_count}, 迁移后 {migrated_count}"
                )

            # 检查每条记录
            original_rule_ids = {
                item.get('rule_id') 
                for item in original_data
                if isinstance(item, dict) and item.get('rule_id')
            }
            migrated_rule_ids = {detail.rule_detail_id for detail in migrated_details}

            # 检查缺失的记录
            missing_ids = original_rule_ids - migrated_rule_ids
            if missing_ids:
                validation_result["passed"] = False
                validation_result["missing_details"].extend(list(missing_ids))

            # 检查多余的记录
            extra_ids = migrated_rule_ids - original_rule_ids
            if extra_ids:
                validation_result["passed"] = False
                validation_result["extra_details"].extend(list(extra_ids))

            return validation_result

        except Exception as e:
            validation_result["passed"] = False
            validation_result["integrity_issues"].append(f"验证过程异常: {str(e)}")
            return validation_result

    def _finalize_migration(self):
        """完成迁移统计"""
        self.migration_stats["end_time"] = datetime.now()
        if self.migration_stats["start_time"]:
            duration = self.migration_stats["end_time"] - self.migration_stats["start_time"]
            self.migration_stats["duration_seconds"] = duration.total_seconds()

    def _safe_int(self, value: Any) -> int | None:
        """安全转换为整数"""
        if value is None or value == "":
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None

    def _safe_bool(self, value: Any) -> bool:
        """安全转换为布尔值"""
        if value is None:
            return False
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on', '是', '真')
        try:
            return bool(int(value))
        except (ValueError, TypeError):
            return False

    def _safe_decimal(self, value: Any) -> Decimal | None:
        """安全转换为 Decimal"""
        if value is None or value == "":
            return None
        try:
            return Decimal(str(value))
        except (InvalidOperation, ValueError, TypeError):
            return None

    def _safe_datetime(self, value: Any) -> datetime | None:
        """安全转换为 datetime"""
        if value is None or value == "":
            return None

        if isinstance(value, datetime):
            return value

        if isinstance(value, str):
            # 尝试多种时间格式
            formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M",
                "%Y-%m-%d",
                "%Y/%m/%d %H:%M:%S",
                "%Y/%m/%d %H:%M",
                "%Y/%m/%d",
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(value, fmt)
                except ValueError:
                    continue

        # 尝试时间戳转换
        try:
            timestamp = float(value)
            # 处理毫秒时间戳
            if timestamp > 1e10:
                timestamp = timestamp / 1000
            return datetime.fromtimestamp(timestamp)
        except (ValueError, TypeError, OSError):
            pass

        return None

    def get_migration_progress(self) -> dict[str, Any]:
        """获取迁移进度信息"""
        progress = 0
        if self.migration_stats["total_datasets"] > 0:
            progress = (self.migration_stats["processed_datasets"] /
                       self.migration_stats["total_datasets"] * 100)

        return {
            "progress_percentage": round(progress, 2),
            "processed_datasets": self.migration_stats["processed_datasets"],
            "total_datasets": self.migration_stats["total_datasets"],
            "successful_datasets": self.migration_stats["successful_datasets"],
            "failed_datasets": self.migration_stats["failed_datasets"],
            "total_details": self.migration_stats["total_details"],
            "duration_seconds": self.migration_stats["duration_seconds"],
            "estimated_remaining_seconds": self._estimate_remaining_time(),
            "current_status": self._get_current_status()
        }

    def _estimate_remaining_time(self) -> float | None:
        """估算剩余时间"""
        if (self.migration_stats["processed_datasets"] == 0 or
            not self.migration_stats["start_time"]):
            return None

        elapsed = (datetime.now() - self.migration_stats["start_time"]).total_seconds()
        avg_time_per_dataset = elapsed / self.migration_stats["processed_datasets"]
        remaining_datasets = (self.migration_stats["total_datasets"] -
                            self.migration_stats["processed_datasets"])

        return avg_time_per_dataset * remaining_datasets

    def _get_current_status(self) -> str:
        """获取当前状态"""
        if self.migration_stats["end_time"]:
            return "completed"
        elif self.migration_stats["processed_datasets"] > 0:
            return "in_progress"
        else:
            return "pending"
