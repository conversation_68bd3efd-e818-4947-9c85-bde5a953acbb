/**
 * 规则注册任务管理 Composable
 * 提供任务状态查询、进度跟踪、自动刷新等功能
 */

import { ref, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getRegistrationTaskStatus, 
  getRegistrationTaskList, 
  cancelRegistrationTask,
  getRegistrationHealth
} from '@/api/rules'

/**
 * 规则注册任务管理
 */
export function useRegistrationTask() {
  // 响应式数据
  const taskInfo = ref(null)
  const taskList = ref([])
  const loading = ref(false)
  const refreshing = ref(false)
  const error = ref(null)
  
  // 自动刷新相关
  const autoRefreshEnabled = ref(false)
  const autoRefreshInterval = ref(3000) // 3秒
  let autoRefreshTimer = null

  // 计算属性
  const hasTask = computed(() => !!taskInfo.value)
  
  const isTaskRunning = computed(() => {
    return taskInfo.value && ['pending', 'running'].includes(taskInfo.value.status)
  })
  
  const isTaskCompleted = computed(() => {
    return taskInfo.value && taskInfo.value.status === 'completed'
  })
  
  const isTaskFailed = computed(() => {
    return taskInfo.value && taskInfo.value.status === 'failed'
  })
  
  const taskProgress = computed(() => {
    return taskInfo.value?.progress_percentage || 0
  })
  
  const taskStats = computed(() => {
    return taskInfo.value?.stats || {}
  })

  // 方法
  
  /**
   * 获取任务状态
   */
  const fetchTaskStatus = async (taskId) => {
    if (!taskId) {
      error.value = '任务ID不能为空'
      return false
    }

    try {
      refreshing.value = true
      error.value = null
      
      const response = await getRegistrationTaskStatus(taskId)
      taskInfo.value = response
      
      return true
    } catch (err) {
      console.error('获取任务状态失败:', err)
      error.value = err.message || '获取任务状态失败'
      
      // 如果是404错误，说明任务不存在
      if (err.response?.status === 404) {
        ElMessage.error('任务不存在或已被删除')
        taskInfo.value = null
      } else {
        ElMessage.error('获取任务状态失败，请稍后重试')
      }
      
      return false
    } finally {
      refreshing.value = false
    }
  }

  /**
   * 获取任务列表
   */
  const fetchTaskList = async (params = {}) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await getRegistrationTaskList(params)
      taskList.value = response.tasks || []
      
      return response
    } catch (err) {
      console.error('获取任务列表失败:', err)
      error.value = err.message || '获取任务列表失败'
      ElMessage.error('获取任务列表失败，请稍后重试')
      
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * 取消任务
   */
  const cancelTask = async (taskId) => {
    if (!taskId) {
      ElMessage.error('任务ID不能为空')
      return false
    }

    try {
      loading.value = true
      
      await cancelRegistrationTask(taskId)
      ElMessage.success('任务取消成功')
      
      // 刷新任务状态
      await fetchTaskStatus(taskId)
      
      return true
    } catch (err) {
      console.error('取消任务失败:', err)
      ElMessage.error('取消任务失败，请稍后重试')
      
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 检查注册服务健康状态
   */
  const checkHealth = async () => {
    try {
      const response = await getRegistrationHealth()
      return response
    } catch (err) {
      console.error('检查服务健康状态失败:', err)
      return null
    }
  }

  /**
   * 开始自动刷新
   */
  const startAutoRefresh = (taskId) => {
    if (!taskId || autoRefreshTimer) return
    
    autoRefreshEnabled.value = true
    autoRefreshTimer = setInterval(async () => {
      if (isTaskRunning.value) {
        await fetchTaskStatus(taskId)
      } else {
        // 任务已结束，停止自动刷新
        stopAutoRefresh()
      }
    }, autoRefreshInterval.value)
  }

  /**
   * 停止自动刷新
   */
  const stopAutoRefresh = () => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer)
      autoRefreshTimer = null
    }
    autoRefreshEnabled.value = false
  }

  /**
   * 切换自动刷新
   */
  const toggleAutoRefresh = (taskId, enabled) => {
    if (enabled) {
      startAutoRefresh(taskId)
    } else {
      stopAutoRefresh()
    }
  }

  /**
   * 重置状态
   */
  const reset = () => {
    taskInfo.value = null
    taskList.value = []
    error.value = null
    loading.value = false
    refreshing.value = false
    stopAutoRefresh()
  }

  /**
   * 轮询任务状态直到完成
   */
  const pollTaskUntilComplete = async (taskId, options = {}) => {
    const { 
      maxAttempts = 100, 
      interval = 3000,
      onProgress = null 
    } = options

    let attempts = 0
    
    while (attempts < maxAttempts) {
      const success = await fetchTaskStatus(taskId)
      
      if (!success) {
        break
      }
      
      // 调用进度回调
      if (onProgress && typeof onProgress === 'function') {
        onProgress(taskInfo.value)
      }
      
      // 检查任务是否已完成
      if (!isTaskRunning.value) {
        break
      }
      
      // 等待指定间隔
      await new Promise(resolve => setTimeout(resolve, interval))
      attempts++
    }
    
    return taskInfo.value
  }

  // 清理函数
  onUnmounted(() => {
    stopAutoRefresh()
  })

  return {
    // 响应式数据
    taskInfo,
    taskList,
    loading,
    refreshing,
    error,
    autoRefreshEnabled,
    autoRefreshInterval,
    
    // 计算属性
    hasTask,
    isTaskRunning,
    isTaskCompleted,
    isTaskFailed,
    taskProgress,
    taskStats,
    
    // 方法
    fetchTaskStatus,
    fetchTaskList,
    cancelTask,
    checkHealth,
    startAutoRefresh,
    stopAutoRefresh,
    toggleAutoRefresh,
    reset,
    pollTaskUntilComplete
  }
}

/**
 * 任务状态格式化工具
 */
export function useTaskStatusFormatter() {
  const statusMap = {
    'pending': { text: '等待中', type: 'info', color: '#909399' },
    'running': { text: '处理中', type: 'warning', color: '#E6A23C' },
    'completed': { text: '已完成', type: 'success', color: '#67C23A' },
    'failed': { text: '失败', type: 'danger', color: '#F56C6C' },
    'cancelled': { text: '已取消', type: 'info', color: '#909399' }
  }

  const formatStatus = (status) => {
    return statusMap[status] || { text: '未知', type: 'info', color: '#909399' }
  }

  const formatDuration = (seconds) => {
    if (!seconds || seconds < 0) return '-'
    
    if (seconds < 60) {
      return `${seconds.toFixed(1)}秒`
    }
    
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    
    if (minutes < 60) {
      return `${minutes}分${remainingSeconds}秒`
    }
    
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    
    return `${hours}小时${remainingMinutes}分`
  }

  const formatTime = (timestamp) => {
    if (!timestamp) return '-'
    
    try {
      return new Date(timestamp).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch (err) {
      return '-'
    }
  }

  const formatProgress = (percentage) => {
    if (typeof percentage !== 'number') return 0
    return Math.max(0, Math.min(100, Math.round(percentage)))
  }

  return {
    formatStatus,
    formatDuration,
    formatTime,
    formatProgress
  }
}
