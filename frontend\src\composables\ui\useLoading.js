/**
 * 统一加载状态管理
 * 提供全局加载状态、智能加载合并、骨架屏和进度条集成
 */

import { ref, computed, watch, onUnmounted } from 'vue'
import { ElLoading } from 'element-plus'
import { useAppStore } from '@/stores/app'

/**
 * 加载类型枚举
 */
export const LoadingTypes = {
  GLOBAL: 'global',           // 全局加载
  LOCAL: 'local',             // 局部加载
  SKELETON: 'skeleton',       // 骨架屏
  PROGRESS: 'progress',       // 进度条
  SPINNER: 'spinner'          // 旋转器
}

/**
 * 默认加载配置
 */
const DEFAULT_LOADING_CONFIG = {
  global: {
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
    lock: true,
    fullscreen: true,
    customClass: 'global-loading'
  },
  local: {
    text: '加载中...',
    background: 'rgba(255, 255, 255, 0.9)',
    lock: false,
    fullscreen: false,
    customClass: 'local-loading'
  },
  skeleton: {
    rows: 3,
    animated: true,
    avatar: false,
    title: true,
    paragraph: true
  },
  progress: {
    type: 'line',
    strokeWidth: 6,
    showText: true,
    textInside: false
  },
  spinner: {
    size: 'default',
    color: '#409eff'
  }
}

/**
 * 统一加载状态管理
 * @param {Object} config - 配置选项
 * @returns {Object} 加载管理实例
 */
export function useLoading(config = {}) {
  const finalConfig = { ...DEFAULT_LOADING_CONFIG, ...config }
  const appStore = useAppStore()
  
  // 加载状态管理
  const loadingInstances = ref(new Map())
  const loadingQueue = ref([])
  const globalLoadingInstance = ref(null)
  const loadingStats = ref({
    totalLoading: 0,
    activeLoading: 0,
    averageLoadingTime: 0,
    longestLoadingTime: 0
  })

  // 智能合并配置
  const mergeConfig = ref({
    enabled: true,              // 是否启用智能合并
    mergeDelay: 100,           // 合并延迟（毫秒）
    maxConcurrent: 3,          // 最大并发加载数
    priorityThreshold: 1000    // 优先级阈值
  })

  // ==================== 核心加载方法 ====================

  /**
   * 显示加载
   * @param {Object} options - 加载选项
   * @returns {Object} 加载实例
   */
  const showLoading = (options = {}) => {
    const loadingOptions = {
      type: LoadingTypes.GLOBAL,
      priority: 0,
      mergeable: true,
      ...finalConfig[options.type || LoadingTypes.GLOBAL],
      ...options
    }

    const loadingId = options.id || `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // 创建加载记录
    const loadingRecord = {
      id: loadingId,
      options: loadingOptions,
      startTime: Date.now(),
      endTime: null,
      duration: null,
      status: 'active',
      instance: null,
      merged: false,
      mergedWith: []
    }

    // 智能合并处理
    if (mergeConfig.value.enabled && loadingOptions.mergeable) {
      const mergeResult = attemptMergeLoading(loadingRecord)
      if (mergeResult.merged) {
        return mergeResult.instance
      }
    }

    // 创建实际的加载实例
    const actualInstance = createLoadingInstance(loadingRecord)
    loadingRecord.instance = actualInstance

    // 注册加载实例
    loadingInstances.value.set(loadingId, loadingRecord)
    updateLoadingStats('start', loadingRecord)

    // 同步到全局状态
    if (loadingOptions.type === LoadingTypes.GLOBAL) {
      appStore.addLoadingTask(loadingId, loadingOptions.text)
    }

    return {
      id: loadingId,
      hide: () => hideLoading(loadingId),
      setText: (text) => setLoadingText(loadingId, text),
      setProgress: (progress) => setLoadingProgress(loadingId, progress),
      update: (newOptions) => updateLoading(loadingId, newOptions),
      getInfo: () => loadingRecord
    }
  }

  /**
   * 隐藏加载
   * @param {string} loadingId - 加载ID
   */
  const hideLoading = (loadingId) => {
    const loadingRecord = loadingInstances.value.get(loadingId)
    if (!loadingRecord) return

    // 更新记录
    loadingRecord.endTime = Date.now()
    loadingRecord.duration = loadingRecord.endTime - loadingRecord.startTime
    loadingRecord.status = 'completed'

    // 关闭实际实例
    if (loadingRecord.instance) {
      try {
        if (typeof loadingRecord.instance.close === 'function') {
          loadingRecord.instance.close()
        } else if (typeof loadingRecord.instance.hide === 'function') {
          loadingRecord.instance.hide()
        }
      } catch (error) {
        console.warn('Error closing loading instance:', error)
      }
    }

    // 处理合并的加载
    if (loadingRecord.mergedWith.length > 0) {
      loadingRecord.mergedWith.forEach(mergedId => {
        const mergedRecord = loadingInstances.value.get(mergedId)
        if (mergedRecord) {
          mergedRecord.endTime = Date.now()
          mergedRecord.duration = mergedRecord.endTime - mergedRecord.startTime
          mergedRecord.status = 'completed'
        }
      })
    }

    // 移除实例
    loadingInstances.value.delete(loadingId)
    updateLoadingStats('end', loadingRecord)

    // 同步到全局状态
    if (loadingRecord.options.type === LoadingTypes.GLOBAL) {
      appStore.removeLoadingTask(loadingId)
    }
  }

  /**
   * 创建加载实例
   * @param {Object} loadingRecord - 加载记录
   * @returns {Object} 加载实例
   */
  const createLoadingInstance = (loadingRecord) => {
    const { options } = loadingRecord

    switch (options.type) {
      case LoadingTypes.GLOBAL:
        return createGlobalLoading(options)
        
      case LoadingTypes.LOCAL:
        return createLocalLoading(options)
        
      case LoadingTypes.SKELETON:
        return createSkeletonLoading(options)
        
      case LoadingTypes.PROGRESS:
        return createProgressLoading(options)
        
      case LoadingTypes.SPINNER:
        return createSpinnerLoading(options)
        
      default:
        return createGlobalLoading(options)
    }
  }

  /**
   * 创建全局加载
   */
  const createGlobalLoading = (options) => {
    if (globalLoadingInstance.value) {
      // 复用现有的全局加载实例
      return globalLoadingInstance.value
    }

    const instance = ElLoading.service({
      ...finalConfig.global,
      ...options,
      target: options.target || document.body
    })

    globalLoadingInstance.value = instance
    return instance
  }

  /**
   * 创建局部加载
   */
  const createLocalLoading = (options) => {
    return ElLoading.service({
      ...finalConfig.local,
      ...options
    })
  }

  /**
   * 创建骨架屏加载
   */
  const createSkeletonLoading = (options) => {
    // 骨架屏需要在组件层面实现，这里返回配置
    return {
      type: LoadingTypes.SKELETON,
      config: { ...finalConfig.skeleton, ...options },
      close: () => {},
      hide: () => {}
    }
  }

  /**
   * 创建进度条加载
   */
  const createProgressLoading = (options) => {
    return {
      type: LoadingTypes.PROGRESS,
      config: { ...finalConfig.progress, ...options },
      progress: 0,
      setProgress: (value) => {
        this.progress = Math.max(0, Math.min(100, value))
        appStore.updateLoadingProgress(this.progress)
      },
      close: () => {},
      hide: () => {}
    }
  }

  /**
   * 创建旋转器加载
   */
  const createSpinnerLoading = (options) => {
    return {
      type: LoadingTypes.SPINNER,
      config: { ...finalConfig.spinner, ...options },
      close: () => {},
      hide: () => {}
    }
  }

  // ==================== 智能合并功能 ====================

  /**
   * 尝试合并加载
   * @param {Object} newLoadingRecord - 新的加载记录
   * @returns {Object} 合并结果
   */
  const attemptMergeLoading = (newLoadingRecord) => {
    const activeLoadings = Array.from(loadingInstances.value.values())
      .filter(record => record.status === 'active' && record.options.mergeable)

    // 检查是否可以合并
    for (const existingRecord of activeLoadings) {
      if (canMergeLoading(existingRecord, newLoadingRecord)) {
        // 执行合并
        existingRecord.mergedWith.push(newLoadingRecord.id)
        newLoadingRecord.merged = true
        newLoadingRecord.mergedWith = [existingRecord.id]
        
        // 更新合并后的文本
        const mergedText = getMergedLoadingText(existingRecord, newLoadingRecord)
        setLoadingText(existingRecord.id, mergedText)

        return {
          merged: true,
          instance: {
            id: newLoadingRecord.id,
            hide: () => hideLoading(newLoadingRecord.id),
            setText: (text) => setLoadingText(existingRecord.id, text),
            setProgress: (progress) => setLoadingProgress(existingRecord.id, progress),
            getInfo: () => newLoadingRecord
          }
        }
      }
    }

    return { merged: false }
  }

  /**
   * 检查是否可以合并加载
   */
  const canMergeLoading = (existing, newLoading) => {
    // 类型必须相同
    if (existing.options.type !== newLoading.options.type) {
      return false
    }

    // 检查并发限制
    if (existing.mergedWith.length >= mergeConfig.value.maxConcurrent) {
      return false
    }

    // 检查优先级
    const priorityDiff = Math.abs(existing.options.priority - newLoading.options.priority)
    if (priorityDiff > mergeConfig.value.priorityThreshold) {
      return false
    }

    // 检查时间间隔
    const timeDiff = newLoading.startTime - existing.startTime
    if (timeDiff > mergeConfig.value.mergeDelay) {
      return false
    }

    return true
  }

  /**
   * 获取合并后的加载文本
   */
  const getMergedLoadingText = (existing, newLoading) => {
    const existingText = existing.options.text || '加载中...'
    const newText = newLoading.options.text || '加载中...'
    
    if (existingText === newText) {
      return existingText
    }
    
    return `${existingText} & ${newText}`
  }

  // ==================== 辅助方法 ====================

  /**
   * 设置加载文本
   */
  const setLoadingText = (loadingId, text) => {
    const loadingRecord = loadingInstances.value.get(loadingId)
    if (loadingRecord && loadingRecord.instance) {
      loadingRecord.options.text = text
      
      // 更新实例文本（如果支持）
      if (typeof loadingRecord.instance.setText === 'function') {
        loadingRecord.instance.setText(text)
      }
    }
  }

  /**
   * 设置加载进度
   */
  const setLoadingProgress = (loadingId, progress) => {
    const loadingRecord = loadingInstances.value.get(loadingId)
    if (loadingRecord && loadingRecord.instance) {
      if (typeof loadingRecord.instance.setProgress === 'function') {
        loadingRecord.instance.setProgress(progress)
      }
      
      // 同步到全局状态
      appStore.updateLoadingProgress(progress)
    }
  }

  /**
   * 更新加载配置
   */
  const updateLoading = (loadingId, newOptions) => {
    const loadingRecord = loadingInstances.value.get(loadingId)
    if (loadingRecord) {
      Object.assign(loadingRecord.options, newOptions)
    }
  }

  /**
   * 更新加载统计
   */
  const updateLoadingStats = (type, loadingRecord) => {
    switch (type) {
      case 'start':
        loadingStats.value.totalLoading++
        loadingStats.value.activeLoading++
        break
        
      case 'end':
        loadingStats.value.activeLoading--
        if (loadingRecord.duration) {
          const totalTime = loadingStats.value.averageLoadingTime * (loadingStats.value.totalLoading - 1) + loadingRecord.duration
          loadingStats.value.averageLoadingTime = totalTime / loadingStats.value.totalLoading
          loadingStats.value.longestLoadingTime = Math.max(loadingStats.value.longestLoadingTime, loadingRecord.duration)
        }
        break
    }
  }

  // ==================== 便捷方法 ====================

  /**
   * 显示全局加载
   */
  const showGlobalLoading = (text = '加载中...', options = {}) => {
    return showLoading({
      type: LoadingTypes.GLOBAL,
      text,
      ...options
    })
  }

  /**
   * 显示局部加载
   */
  const showLocalLoading = (target, text = '加载中...', options = {}) => {
    return showLoading({
      type: LoadingTypes.LOCAL,
      target,
      text,
      ...options
    })
  }

  /**
   * 隐藏所有加载
   */
  const hideAllLoading = () => {
    loadingInstances.value.forEach((record, id) => {
      hideLoading(id)
    })
    
    if (globalLoadingInstance.value) {
      globalLoadingInstance.value.close()
      globalLoadingInstance.value = null
    }
  }

  // ==================== 计算属性 ====================

  /**
   * 是否有活跃的加载
   */
  const hasActiveLoading = computed(() => {
    return loadingInstances.value.size > 0
  })

  /**
   * 活跃加载列表
   */
  const activeLoadings = computed(() => {
    return Array.from(loadingInstances.value.values())
      .filter(record => record.status === 'active')
  })

  /**
   * 加载统计信息
   */
  const stats = computed(() => ({
    ...loadingStats.value,
    activeCount: activeLoadings.value.length,
    mergedCount: activeLoadings.value.filter(record => record.merged || record.mergedWith.length > 0).length
  }))

  // ==================== 生命周期 ====================

  // 组件卸载时清理
  onUnmounted(() => {
    hideAllLoading()
  })

  // 监听全局加载状态
  watch(
    () => appStore.globalLoading,
    (newValue) => {
      if (!newValue && globalLoadingInstance.value) {
        globalLoadingInstance.value.close()
        globalLoadingInstance.value = null
      }
    }
  )

  // ==================== 返回接口 ====================

  return {
    // 核心方法
    showLoading,
    hideLoading,
    hideAllLoading,

    // 便捷方法
    showGlobalLoading,
    showLocalLoading,

    // 配置方法
    setLoadingText,
    setLoadingProgress,
    updateLoading,

    // 状态
    loadingInstances,
    hasActiveLoading,
    activeLoadings,
    stats,

    // 配置
    config: finalConfig,
    mergeConfig,

    // 常量
    LoadingTypes
  }
}
