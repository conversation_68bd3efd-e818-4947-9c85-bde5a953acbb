/**
 * 状态持久化和监控系统
 * 提供状态持久化、性能监控、调试工具和状态恢复机制
 */

import { ref, computed, watch, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'

/**
 * 存储类型枚举
 */
export const StorageTypes = {
  LOCAL: 'localStorage',
  SESSION: 'sessionStorage',
  MEMORY: 'memory',
  INDEXED_DB: 'indexedDB'
}

/**
 * 持久化策略枚举
 */
export const PersistenceStrategies = {
  IMMEDIATE: 'immediate',     // 立即持久化
  DEBOUNCED: 'debounced',    // 防抖持久化
  THROTTLED: 'throttled',    // 节流持久化
  MANUAL: 'manual'           // 手动持久化
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
  storage: StorageTypes.LOCAL,
  strategy: PersistenceStrategies.DEBOUNCED,
  debounceDelay: 1000,
  throttleDelay: 5000,
  keyPrefix: 'app_state_',
  version: '1.0.0',
  enableCompression: true,
  enableEncryption: false,
  maxStorageSize: 5 * 1024 * 1024, // 5MB
  enableMetrics: true,
  enableDebug: process.env.NODE_ENV === 'development'
}

/**
 * 内存存储实现
 */
const memoryStorage = new Map()

/**
 * 状态持久化和监控
 * @param {Object} config - 配置选项
 * @returns {Object} 持久化管理实例
 */
export function useStatePersistence(config = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  const appStore = useAppStore()
  
  // 持久化状态
  const persistenceState = ref({
    isEnabled: true,
    lastSaveTime: null,
    lastLoadTime: null,
    saveCount: 0,
    loadCount: 0,
    errorCount: 0,
    storageUsage: 0
  })

  // 监控指标
  const metrics = ref({
    performance: {
      saveTime: [],
      loadTime: [],
      averageSaveTime: 0,
      averageLoadTime: 0
    },
    storage: {
      totalSize: 0,
      itemCount: 0,
      compressionRatio: 0
    },
    errors: []
  })

  // 调试信息
  const debugInfo = ref({
    enabled: finalConfig.enableDebug,
    logs: [],
    snapshots: [],
    maxLogs: 1000,
    maxSnapshots: 50
  })

  // 定时器引用
  const timers = ref({
    debounce: null,
    throttle: null,
    cleanup: null
  })

  // ==================== 核心持久化方法 ====================

  /**
   * 保存状态
   * @param {string} key - 存储键
   * @param {any} data - 要保存的数据
   * @param {Object} options - 保存选项
   * @returns {Promise<boolean>} 保存是否成功
   */
  const saveState = async (key, data, options = {}) => {
    if (!persistenceState.value.isEnabled) {
      return false
    }

    const startTime = performance.now()
    
    try {
      const saveOptions = { ...finalConfig, ...options }
      const storageKey = `${saveOptions.keyPrefix}${key}`
      
      // 准备数据
      const preparedData = await prepareDataForStorage(data, saveOptions)
      
      // 检查存储大小
      if (preparedData.length > saveOptions.maxStorageSize) {
        throw new Error(`Data size (${preparedData.length}) exceeds maximum storage size (${saveOptions.maxStorageSize})`)
      }

      // 保存到存储
      await writeToStorage(storageKey, preparedData, saveOptions.storage)
      
      // 更新状态
      persistenceState.value.lastSaveTime = Date.now()
      persistenceState.value.saveCount++
      
      // 更新指标
      const saveTime = performance.now() - startTime
      updatePerformanceMetrics('save', saveTime)
      updateStorageMetrics()
      
      // 调试日志
      if (debugInfo.value.enabled) {
        addDebugLog('save', {
          key: storageKey,
          dataSize: preparedData.length,
          saveTime,
          success: true
        })
      }

      return true

    } catch (error) {
      persistenceState.value.errorCount++
      metrics.value.errors.push({
        type: 'save_error',
        error: error.message,
        timestamp: Date.now(),
        key
      })

      if (debugInfo.value.enabled) {
        addDebugLog('save_error', {
          key,
          error: error.message,
          timestamp: Date.now()
        })
      }

      console.error('State save failed:', error)
      return false
    }
  }

  /**
   * 加载状态
   * @param {string} key - 存储键
   * @param {any} defaultValue - 默认值
   * @param {Object} options - 加载选项
   * @returns {Promise<any>} 加载的数据
   */
  const loadState = async (key, defaultValue = null, options = {}) => {
    const startTime = performance.now()
    
    try {
      const loadOptions = { ...finalConfig, ...options }
      const storageKey = `${loadOptions.keyPrefix}${key}`
      
      // 从存储读取
      const rawData = await readFromStorage(storageKey, loadOptions.storage)
      
      if (rawData === null) {
        return defaultValue
      }

      // 解析数据
      const parsedData = await parseDataFromStorage(rawData, loadOptions)
      
      // 验证版本
      if (parsedData.version && parsedData.version !== loadOptions.version) {
        console.warn(`Version mismatch for key ${key}: expected ${loadOptions.version}, got ${parsedData.version}`)
        return defaultValue
      }

      // 检查过期时间
      if (parsedData.expires && parsedData.expires < Date.now()) {
        await removeState(key, options)
        return defaultValue
      }

      // 更新状态
      persistenceState.value.lastLoadTime = Date.now()
      persistenceState.value.loadCount++
      
      // 更新指标
      const loadTime = performance.now() - startTime
      updatePerformanceMetrics('load', loadTime)
      
      // 调试日志
      if (debugInfo.value.enabled) {
        addDebugLog('load', {
          key: storageKey,
          loadTime,
          success: true,
          hasData: true
        })
      }

      return parsedData.data

    } catch (error) {
      persistenceState.value.errorCount++
      metrics.value.errors.push({
        type: 'load_error',
        error: error.message,
        timestamp: Date.now(),
        key
      })

      if (debugInfo.value.enabled) {
        addDebugLog('load_error', {
          key,
          error: error.message,
          timestamp: Date.now()
        })
      }

      console.error('State load failed:', error)
      return defaultValue
    }
  }

  /**
   * 移除状态
   * @param {string} key - 存储键
   * @param {Object} options - 移除选项
   * @returns {Promise<boolean>} 移除是否成功
   */
  const removeState = async (key, options = {}) => {
    try {
      const removeOptions = { ...finalConfig, ...options }
      const storageKey = `${removeOptions.keyPrefix}${key}`
      
      await removeFromStorage(storageKey, removeOptions.storage)
      updateStorageMetrics()
      
      if (debugInfo.value.enabled) {
        addDebugLog('remove', {
          key: storageKey,
          success: true
        })
      }

      return true

    } catch (error) {
      persistenceState.value.errorCount++
      console.error('State remove failed:', error)
      return false
    }
  }

  /**
   * 清除所有状态
   * @param {Object} options - 清除选项
   * @returns {Promise<boolean>} 清除是否成功
   */
  const clearAllStates = async (options = {}) => {
    try {
      const clearOptions = { ...finalConfig, ...options }
      
      if (clearOptions.storage === StorageTypes.MEMORY) {
        memoryStorage.clear()
      } else {
        const storage = getStorageInstance(clearOptions.storage)
        const keysToRemove = []
        
        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i)
          if (key && key.startsWith(clearOptions.keyPrefix)) {
            keysToRemove.push(key)
          }
        }
        
        keysToRemove.forEach(key => storage.removeItem(key))
      }

      updateStorageMetrics()
      
      if (debugInfo.value.enabled) {
        addDebugLog('clear_all', {
          success: true,
          timestamp: Date.now()
        })
      }

      return true

    } catch (error) {
      persistenceState.value.errorCount++
      console.error('Clear all states failed:', error)
      return false
    }
  }

  // ==================== 数据处理方法 ====================

  /**
   * 准备数据用于存储
   */
  const prepareDataForStorage = async (data, options) => {
    const wrappedData = {
      data,
      timestamp: Date.now(),
      version: options.version,
      expires: options.expires || null
    }

    let serializedData = JSON.stringify(wrappedData)

    // 压缩
    if (options.enableCompression) {
      serializedData = await compressData(serializedData)
    }

    // 加密
    if (options.enableEncryption) {
      serializedData = await encryptData(serializedData)
    }

    return serializedData
  }

  /**
   * 从存储解析数据
   */
  const parseDataFromStorage = async (rawData, options) => {
    let processedData = rawData

    // 解密
    if (options.enableEncryption) {
      processedData = await decryptData(processedData)
    }

    // 解压缩
    if (options.enableCompression) {
      processedData = await decompressData(processedData)
    }

    return JSON.parse(processedData)
  }

  // ==================== 存储适配器 ====================

  /**
   * 获取存储实例
   */
  const getStorageInstance = (storageType) => {
    switch (storageType) {
      case StorageTypes.LOCAL:
        return localStorage
      case StorageTypes.SESSION:
        return sessionStorage
      case StorageTypes.MEMORY:
        return memoryStorage
      default:
        return localStorage
    }
  }

  /**
   * 写入存储
   */
  const writeToStorage = async (key, data, storageType) => {
    const storage = getStorageInstance(storageType)
    
    if (storageType === StorageTypes.MEMORY) {
      storage.set(key, data)
    } else {
      storage.setItem(key, data)
    }
  }

  /**
   * 从存储读取
   */
  const readFromStorage = async (key, storageType) => {
    const storage = getStorageInstance(storageType)
    
    if (storageType === StorageTypes.MEMORY) {
      return storage.get(key) || null
    } else {
      return storage.getItem(key)
    }
  }

  /**
   * 从存储移除
   */
  const removeFromStorage = async (key, storageType) => {
    const storage = getStorageInstance(storageType)
    
    if (storageType === StorageTypes.MEMORY) {
      storage.delete(key)
    } else {
      storage.removeItem(key)
    }
  }

  // ==================== 压缩和加密 ====================

  /**
   * 压缩数据（简单实现）
   */
  const compressData = async (data) => {
    // 这里可以集成真正的压缩库，如 pako
    return data
  }

  /**
   * 解压缩数据
   */
  const decompressData = async (data) => {
    return data
  }

  /**
   * 加密数据（简单实现）
   */
  const encryptData = async (data) => {
    // 这里可以集成真正的加密库
    return btoa(data)
  }

  /**
   * 解密数据
   */
  const decryptData = async (data) => {
    try {
      return atob(data)
    } catch {
      return data
    }
  }

  // ==================== 指标和监控 ====================

  /**
   * 更新性能指标
   */
  const updatePerformanceMetrics = (type, time) => {
    const performanceData = metrics.value.performance
    
    if (type === 'save') {
      performanceData.saveTime.push(time)
      if (performanceData.saveTime.length > 100) {
        performanceData.saveTime = performanceData.saveTime.slice(-50)
      }
      performanceData.averageSaveTime = performanceData.saveTime.reduce((a, b) => a + b, 0) / performanceData.saveTime.length
    } else if (type === 'load') {
      performanceData.loadTime.push(time)
      if (performanceData.loadTime.length > 100) {
        performanceData.loadTime = performanceData.loadTime.slice(-50)
      }
      performanceData.averageLoadTime = performanceData.loadTime.reduce((a, b) => a + b, 0) / performanceData.loadTime.length
    }
  }

  /**
   * 更新存储指标
   */
  const updateStorageMetrics = () => {
    try {
      const storage = getStorageInstance(finalConfig.storage)
      let totalSize = 0
      let itemCount = 0

      if (finalConfig.storage === StorageTypes.MEMORY) {
        itemCount = storage.size
        for (const [key, value] of storage) {
          if (key.startsWith(finalConfig.keyPrefix)) {
            totalSize += new Blob([value]).size
          }
        }
      } else {
        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i)
          if (key && key.startsWith(finalConfig.keyPrefix)) {
            const value = storage.getItem(key)
            if (value) {
              totalSize += new Blob([value]).size
              itemCount++
            }
          }
        }
      }

      metrics.value.storage.totalSize = totalSize
      metrics.value.storage.itemCount = itemCount
      persistenceState.value.storageUsage = totalSize

    } catch (error) {
      console.warn('Failed to update storage metrics:', error)
    }
  }

  /**
   * 添加调试日志
   */
  const addDebugLog = (type, data) => {
    if (!debugInfo.value.enabled) return

    const logEntry = {
      type,
      data,
      timestamp: Date.now()
    }

    debugInfo.value.logs.push(logEntry)

    // 限制日志数量
    if (debugInfo.value.logs.length > debugInfo.value.maxLogs) {
      debugInfo.value.logs = debugInfo.value.logs.slice(-debugInfo.value.maxLogs / 2)
    }
  }

  // ==================== 自动持久化 ====================

  /**
   * 设置自动持久化
   * @param {string} key - 存储键
   * @param {Function} getter - 获取数据的函数
   * @param {Object} options - 选项
   * @returns {Function} 停止监听的函数
   */
  const setupAutoPersistence = (key, getter, options = {}) => {
    const autoOptions = { ...finalConfig, ...options }
    
    let lastValue = null
    
    const persistData = async () => {
      const currentValue = getter()
      if (currentValue !== lastValue) {
        await saveState(key, currentValue, autoOptions)
        lastValue = currentValue
      }
    }

    switch (autoOptions.strategy) {
      case PersistenceStrategies.IMMEDIATE:
        return watch(getter, persistData, { deep: true })
        
      case PersistenceStrategies.DEBOUNCED:
        return watch(getter, () => {
          if (timers.value.debounce) {
            clearTimeout(timers.value.debounce)
          }
          timers.value.debounce = setTimeout(persistData, autoOptions.debounceDelay)
        }, { deep: true })
        
      case PersistenceStrategies.THROTTLED:
        let lastExecution = 0
        return watch(getter, () => {
          const now = Date.now()
          if (now - lastExecution >= autoOptions.throttleDelay) {
            persistData()
            lastExecution = now
          }
        }, { deep: true })
        
      default:
        return () => {} // Manual strategy
    }
  }

  // ==================== 计算属性 ====================

  /**
   * 存储使用率
   */
  const storageUsagePercentage = computed(() => {
    return finalConfig.maxStorageSize > 0 
      ? (persistenceState.value.storageUsage / finalConfig.maxStorageSize) * 100 
      : 0
  })

  /**
   * 性能统计
   */
  const performanceStats = computed(() => ({
    ...metrics.value.performance,
    totalOperations: persistenceState.value.saveCount + persistenceState.value.loadCount,
    errorRate: persistenceState.value.errorCount > 0 
      ? (persistenceState.value.errorCount / (persistenceState.value.saveCount + persistenceState.value.loadCount)) * 100 
      : 0
  }))

  // ==================== 生命周期 ====================

  // 初始化时更新存储指标
  updateStorageMetrics()

  // 定期清理过期数据
  timers.value.cleanup = setInterval(() => {
    // 这里可以添加清理过期数据的逻辑
  }, 60000) // 每分钟检查一次

  // 组件卸载时清理
  onUnmounted(() => {
    Object.values(timers.value).forEach(timer => {
      if (timer) clearTimeout(timer)
    })
  })

  // ==================== 返回接口 ====================

  return {
    // 核心方法
    saveState,
    loadState,
    removeState,
    clearAllStates,

    // 自动持久化
    setupAutoPersistence,

    // 状态和指标
    persistenceState,
    metrics,
    debugInfo,
    storageUsagePercentage,
    performanceStats,

    // 配置
    config: finalConfig,
    
    // 常量
    StorageTypes,
    PersistenceStrategies
  }
}
