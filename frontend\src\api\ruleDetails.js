/**
 * 规则明细管理 API 模块
 * 专门处理规则明细的 CRUD 操作、批量处理、统计分析等功能
 */

import { get, post, put, del } from './request'
import { withErrorHandling } from '../utils/apiErrorHandler'

// 错误处理配置
const errorConfig = {
  showMessage: true,
  showNotification: false,
  enableRetry: true,
  retryCount: 2,
  logError: true
}

// ==================== 基础 CRUD 操作 ====================

/**
 * 获取规则明细列表
 * @param {string} ruleKey - 规则键
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 规则明细列表（包含分页信息）
 */
export const getRuleDetailsList = withErrorHandling(
  (ruleKey, params = {}) => get(`/v1/rules/details/${ruleKey}`, params),
  { ...errorConfig, customMessage: '获取规则明细列表失败' }
)

/**
 * 获取单条规则明细
 * @param {string} ruleKey - 规则键
 * @param {string|number} detailId - 明细ID
 * @returns {Promise<Object>} 规则明细详情
 */
export const getRuleDetailById = withErrorHandling(
  (ruleKey, detailId) => get(`/v1/rules/details/${ruleKey}/${detailId}`),
  { ...errorConfig, customMessage: '获取规则明细详情失败' }
)

/**
 * 创建规则明细
 * @param {string} ruleKey - 规则键
 * @param {Object} data - 规则明细数据
 * @returns {Promise<Object>} 创建结果
 */
export const createRuleDetail = withErrorHandling(
  (ruleKey, data) => post(`/v1/rules/details/${ruleKey}`, data),
  { ...errorConfig, customMessage: '创建规则明细失败' }
)

/**
 * 更新规则明细
 * @param {string} ruleKey - 规则键
 * @param {string|number} detailId - 明细ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateRuleDetail = withErrorHandling(
  (ruleKey, detailId, data) => put(`/v1/rules/details/${ruleKey}/${detailId}`, data),
  { ...errorConfig, customMessage: '更新规则明细失败' }
)

/**
 * 删除规则明细
 * @param {string} ruleKey - 规则键
 * @param {string|number} detailId - 明细ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteRuleDetail = withErrorHandling(
  (ruleKey, detailId) => del(`/v1/rules/details/${ruleKey}/${detailId}`),
  { ...errorConfig, customMessage: '删除规则明细失败' }
)

// ==================== 批量操作 ====================

/**
 * 批量操作规则明细
 * @param {string} ruleKey - 规则键
 * @param {Object} data - 批量操作数据
 * @returns {Promise<Object>} 操作结果
 */
export function batchOperateRuleDetails(ruleKey, data) {
  return post(`/v1/rules/${ruleKey}/details/batch`, data)
}

/**
 * 批量创建规则明细
 * @param {string} ruleKey - 规则键
 * @param {Array} details - 规则明细数组
 * @returns {Promise<Object>} 创建结果
 */
export function batchCreateRuleDetails(ruleKey, details) {
  const operations = details.map(data => ({
    action: 'CREATE',
    data
  }))
  return batchOperateRuleDetails(ruleKey, { operations })
}

/**
 * 批量更新规则明细
 * @param {string} ruleKey - 规则键
 * @param {Array} updates - 更新数据数组 [{id, data}]
 * @returns {Promise<Object>} 更新结果
 */
export function batchUpdateRuleDetails(ruleKey, updates) {
  const operations = updates.map(({ id, data }) => ({
    action: 'UPDATE',
    id,
    data
  }))
  return batchOperateRuleDetails(ruleKey, { operations })
}

/**
 * 批量删除规则明细
 * @param {string} ruleKey - 规则键
 * @param {Array} detailIds - 明细ID数组
 * @returns {Promise<Object>} 删除结果
 */
export function batchDeleteRuleDetails(ruleKey, detailIds) {
  const operations = detailIds.map(id => ({
    action: 'DELETE',
    id
  }))
  return batchOperateRuleDetails(ruleKey, { operations })
}

// ==================== 增量操作 ====================

/**
 * 增量上传规则明细
 * @param {string} ruleKey - 规则键
 * @param {Object} data - 增量数据
 * @returns {Promise<Object>} 上传结果
 */
export function incrementalUploadRuleDetails(ruleKey, data) {
  return post(`/v1/rules/${ruleKey}/details/incremental`, data)
}

// ==================== 查询和搜索 ====================

/**
 * 搜索规则明细
 * @param {string} ruleKey - 规则键
 * @param {Object} searchParams - 搜索参数
 * @returns {Promise<Object>} 搜索结果
 */
export function searchRuleDetails(ruleKey, searchParams) {
  return get(`/v1/rules/${ruleKey}/details/search`, searchParams)
}

/**
 * 高级查询规则明细
 * @param {string} ruleKey - 规则键
 * @param {Object} queryParams - 查询参数
 * @returns {Promise<Object>} 查询结果
 */
export function queryRuleDetails(ruleKey, queryParams) {
  return post(`/v1/rules/${ruleKey}/details/query`, queryParams)
}

// ==================== 统计和分析 ====================

/**
 * 获取规则明细统计信息
 * @param {string} ruleKey - 规则键
 * @returns {Promise<Object>} 统计信息
 */
export function getRuleDetailsStatistics(ruleKey) {
  return get(`/v1/rules/${ruleKey}/details/statistics`)
}

/**
 * 获取规则明细分布统计
 * @param {string} ruleKey - 规则键
 * @param {string} dimension - 统计维度 (status, error_level, category, etc.)
 * @returns {Promise<Object>} 分布统计
 */
export function getRuleDetailsDistribution(ruleKey, dimension) {
  return get(`/v1/rules/${ruleKey}/details/distribution`, { dimension })
}

// ==================== 导入导出 ====================

/**
 * 导出规则明细
 * @param {string} ruleKey - 规则键
 * @param {Object} params - 导出参数
 * @returns {Promise<Object>} 导出结果
 */
export function exportRuleDetails(ruleKey, params = {}) {
  return get(`/v1/rules/${ruleKey}/details/export`, params)
}

/**
 * 下载规则明细模板
 * @param {string} ruleKey - 规则键
 * @returns {Promise<Object>} 模板下载
 */
export function downloadRuleDetailsTemplate(ruleKey) {
  return get(`/v1/rules/${ruleKey}/details/template`)
}

// ==================== 配置和元数据 ====================

/**
 * 获取规则明细字段配置
 * @param {string} ruleKey - 规则键
 * @returns {Promise<Object>} 字段配置
 */
export function getRuleDetailsFieldConfig(ruleKey) {
  return get(`/v1/rules/${ruleKey}/details/fields`)
}

/**
 * 获取规则明细枚举值
 * @param {string} ruleKey - 规则键
 * @param {string} fieldName - 字段名
 * @returns {Promise<Object>} 枚举值列表
 */
export function getRuleDetailsEnumValues(ruleKey, fieldName) {
  return get(`/v1/rules/${ruleKey}/details/enums/${fieldName}`)
}

// ==================== 验证和校验 ====================

/**
 * 验证规则明细数据
 * @param {string} ruleKey - 规则键
 * @param {Array|Object} data - 待验证数据
 * @returns {Promise<Object>} 验证结果
 */
export function validateRuleDetailsData(ruleKey, data) {
  return post(`/v1/rules/${ruleKey}/details/validate`, { data })
}

/**
 * 预览规则明细导入
 * @param {string} ruleKey - 规则键
 * @param {Array} data - 导入数据
 * @returns {Promise<Object>} 预览结果
 */
export function previewRuleDetailsImport(ruleKey, data) {
  return post(`/v1/rules/${ruleKey}/details/preview`, { data })
}

// ==================== 历史和审计 ====================

/**
 * 获取规则明细变更历史
 * @param {string} ruleKey - 规则键
 * @param {string|number} detailId - 明细ID
 * @returns {Promise<Object>} 变更历史
 */
export function getRuleDetailHistory(ruleKey, detailId) {
  return get(`/v1/rules/${ruleKey}/details/${detailId}/history`)
}

/**
 * 获取规则明细操作日志
 * @param {string} ruleKey - 规则键
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 操作日志
 */
export function getRuleDetailsAuditLog(ruleKey, params = {}) {
  return get(`/v1/rules/${ruleKey}/details/audit-log`, params)
}
