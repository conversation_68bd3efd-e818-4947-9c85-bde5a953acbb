import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 格式化日期
 * @param {string|number|Date} date - 日期
 * @param {string} format - 格式化模板
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '-'
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param {string|number|Date} date - 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return '-'
  return dayjs(date).fromNow()
}

/**
 * 格式化日期为简短格式
 * @param {string|number|Date} date - 日期
 * @returns {string} 简短日期字符串
 */
export function formatDateShort(date) {
  if (!date) return '-'
  const now = dayjs()
  const target = dayjs(date)
  
  if (now.isSame(target, 'day')) {
    return target.format('HH:mm')
  } else if (now.isSame(target, 'year')) {
    return target.format('MM-DD HH:mm')
  } else {
    return target.format('YYYY-MM-DD')
  }
}

/**
 * 计算时间差
 * @param {string|number|Date} startDate - 开始时间
 * @param {string|number|Date} endDate - 结束时间
 * @param {string} unit - 单位 (millisecond, second, minute, hour, day)
 * @returns {number} 时间差
 */
export function timeDiff(startDate, endDate, unit = 'millisecond') {
  if (!startDate || !endDate) return 0
  return dayjs(endDate).diff(dayjs(startDate), unit)
}

/**
 * 判断日期是否有效
 * @param {any} date - 日期
 * @returns {boolean} 是否有效
 */
export function isValidDate(date) {
  return dayjs(date).isValid()
}

/**
 * 获取当前时间戳
 * @returns {number} 时间戳
 */
export function getCurrentTimestamp() {
  return dayjs().valueOf()
}

/**
 * 格式化持续时间
 * @param {number} duration - 持续时间（毫秒）
 * @returns {string} 格式化后的持续时间
 */
export function formatDuration(duration) {
  if (!duration || duration < 0) return '0秒'
  
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}
