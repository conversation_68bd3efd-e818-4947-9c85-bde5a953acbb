<template>
  <div class="degradation-status">
    <!-- 状态概览卡片 -->
    <el-card class="status-overview" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="title">系统降级状态</span>
          <div class="actions">
            <el-button 
              :icon="Refresh" 
              circle 
              size="small" 
              :loading="loading.status"
              @click="refreshStatus"
              title="刷新状态"
            />
            <el-switch
              v-model="autoRefreshEnabled"
              @change="toggleAutoRefresh"
              active-text="自动刷新"
              inactive-text=""
              size="small"
            />
          </div>
        </div>
      </template>

      <div class="status-content">
        <!-- 当前状态 -->
        <div class="current-status">
          <div class="status-badge">
            <el-tag 
              :type="getStatusTagType(currentStatus.current_level)"
              size="large"
              effect="dark"
            >
              {{ formatDegradationLevel(currentStatus.current_level) }}
            </el-tag>
          </div>
          
          <div class="status-info">
            <div class="info-item">
              <span class="label">状态:</span>
              <span class="value">{{ isSystemDegraded ? '降级中' : '正常运行' }}</span>
            </div>
            
            <div class="info-item" v-if="currentStatus.degradation_duration > 0">
              <span class="label">持续时间:</span>
              <span class="value">{{ formatDuration(currentStatus.degradation_duration) }}</span>
            </div>
            
            <div class="info-item" v-if="currentStatus.active_triggers.length > 0">
              <span class="label">触发器:</span>
              <span class="value">
                <el-tag 
                  v-for="trigger in currentStatus.active_triggers" 
                  :key="trigger"
                  size="small"
                  type="warning"
                  class="trigger-tag"
                >
                  {{ formatTriggerType(trigger) }}
                </el-tag>
              </span>
            </div>
            
            <div class="info-item" v-if="currentStatus.is_manual_override">
              <span class="label">手动干预:</span>
              <span class="value">{{ currentStatus.override_reason || '是' }}</span>
            </div>
          </div>
        </div>

        <!-- 系统指标 -->
        <div class="system-metrics">
          <div class="metric-item">
            <div class="metric-value">{{ currentStatus.executed_actions_count }}</div>
            <div class="metric-label">已执行动作</div>
          </div>
          
          <div class="metric-item">
            <div class="metric-value">{{ degradedComponentsCount }}/{{ totalComponentsCount }}</div>
            <div class="metric-label">降级组件</div>
          </div>
          
          <div class="metric-item">
            <div class="metric-value">{{ degradationSuccessRate }}%</div>
            <div class="metric-label">降级成功率</div>
          </div>
          
          <div class="metric-item">
            <div class="metric-value">{{ recoverySuccessRate }}%</div>
            <div class="metric-label">恢复成功率</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 组件状态列表 -->
    <el-card class="components-status" shadow="hover" v-if="componentsStatus.length > 0">
      <template #header>
        <span class="title">组件状态</span>
      </template>

      <div class="components-list">
        <div 
          v-for="component in componentsStatus" 
          :key="component.component_name"
          class="component-item"
          :class="{ 'degraded': component.is_degraded }"
        >
          <div class="component-header">
            <div class="component-name">{{ component.component_name }}</div>
            <el-tag 
              :type="getStatusTagType(component.current_level)"
              size="small"
            >
              {{ formatDegradationLevel(component.current_level) }}
            </el-tag>
          </div>
          
          <div class="component-details" v-if="component.is_degraded">
            <div class="detail-item">
              <span class="label">降级原因:</span>
              <span class="value">{{ component.degradation_reason }}</span>
            </div>
            
            <div class="detail-item">
              <span class="label">变更时间:</span>
              <span class="value">{{ formatTimestamp(component.last_change_time) }}</span>
            </div>
            
            <!-- 配置对比 -->
            <div class="config-comparison" v-if="Object.keys(component.degraded_config).length > 0">
              <div class="config-title">配置变更:</div>
              <div class="config-items">
                <div 
                  v-for="(value, key) in component.degraded_config" 
                  :key="key"
                  class="config-item"
                >
                  <span class="config-key">{{ key }}:</span>
                  <span class="config-original">{{ component.original_config[key] }}</span>
                  <el-icon class="config-arrow"><ArrowRight /></el-icon>
                  <span class="config-degraded">{{ value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 性能影响 -->
    <el-card class="performance-impact" shadow="hover" v-if="performanceImpact.estimated_performance_reduction > 0">
      <template #header>
        <span class="title">性能影响评估</span>
      </template>

      <div class="impact-content">
        <div class="impact-overview">
          <div class="impact-value">
            {{ (performanceImpact.estimated_performance_reduction * 100).toFixed(1) }}%
          </div>
          <div class="impact-label">预估性能降低</div>
        </div>

        <div class="resource-savings" v-if="performanceImpact.resource_savings">
          <div class="savings-title">资源节省:</div>
          <div class="savings-items">
            <div class="savings-item">
              <span class="label">CPU:</span>
              <span class="value">{{ (performanceImpact.resource_savings.cpu_usage_reduction * 100).toFixed(1) }}%</span>
            </div>
            <div class="savings-item">
              <span class="label">内存:</span>
              <span class="value">{{ (performanceImpact.resource_savings.memory_usage_reduction * 100).toFixed(1) }}%</span>
            </div>
            <div class="savings-item">
              <span class="label">网络:</span>
              <span class="value">{{ (performanceImpact.resource_savings.network_usage_reduction * 100).toFixed(1) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { Refresh, ArrowRight } from '@element-plus/icons-vue'
import { useDegradationStore } from '../../stores/degradation'
import { 
  formatDegradationLevel, 
  formatTriggerType, 
  formatDuration, 
  formatTimestamp,
  getDegradationLevelColor 
} from '../../api/degradation'

// 使用降级状态 store
const degradationStore = useDegradationStore()

// 从 store 中获取状态和方法
const {
  currentStatus,
  componentsStatus,
  performanceImpact,
  loading,
  autoRefreshEnabled,
  isSystemDegraded,
  degradedComponentsCount,
  totalComponentsCount,
  degradationSuccessRate,
  recoverySuccessRate,
  fetchAllData,
  toggleAutoRefresh,
  startAutoRefresh,
  stopAutoRefresh
} = degradationStore

// 刷新状态
const refreshStatus = async () => {
  await fetchAllData()
}

// 获取状态标签类型
const getStatusTagType = (level) => {
  return getDegradationLevelColor(level)
}

// 组件挂载时初始化
onMounted(async () => {
  await fetchAllData()
  startAutoRefresh()
})

// 组件卸载时清理
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.degradation-status {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.current-status {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-badge {
  flex-shrink: 0;
}

.status-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 80px;
}

.info-item .value {
  color: var(--el-text-color-primary);
}

.trigger-tag {
  margin-right: 4px;
}

.system-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-color-primary);
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.components-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.component-item {
  padding: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  transition: all 0.3s;
}

.component-item.degraded {
  border-color: var(--el-color-warning);
  background: var(--el-color-warning-light-9);
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.component-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.component-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 80px;
}

.config-comparison {
  margin-top: 12px;
}

.config-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.config-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.config-key {
  font-weight: 500;
  min-width: 100px;
}

.config-original {
  color: var(--el-text-color-regular);
}

.config-arrow {
  color: var(--el-color-warning);
}

.config-degraded {
  color: var(--el-color-warning);
  font-weight: 500;
}

.impact-content {
  display: flex;
  align-items: center;
  gap: 30px;
}

.impact-overview {
  text-align: center;
}

.impact-value {
  font-size: 32px;
  font-weight: 600;
  color: var(--el-color-warning);
  margin-bottom: 4px;
}

.impact-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.resource-savings {
  flex: 1;
}

.savings-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.savings-items {
  display: flex;
  gap: 20px;
}

.savings-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.savings-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.savings-item .value {
  color: var(--el-color-success);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .current-status {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .system-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .impact-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .savings-items {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
