<template>
  <div class="batch-progress-monitor">
    <el-card shadow="never" class="progress-card">
      <template #header>
        <div class="progress-header">
          <div class="header-left">
            <el-icon class="header-icon" :class="statusIconClass">
              <component :is="statusIcon" />
            </el-icon>
            <span class="header-title">{{ operationTitle }}</span>
          </div>
          <div class="header-right">
            <el-tag :type="statusTagType" size="small">
              {{ statusText }}
            </el-tag>
          </div>
        </div>
      </template>

      <!-- 总体进度 -->
      <div class="overall-progress">
        <div class="progress-info">
          <span class="progress-label">总体进度</span>
          <span class="progress-text">
            {{ completedCount }} / {{ totalCount }} 
            ({{ progressPercentage }}%)
          </span>
        </div>
        <el-progress 
          :percentage="progressPercentage" 
          :status="progressStatus"
          :stroke-width="8"
          :show-text="false"
        />
      </div>

      <!-- 详细进度信息 -->
      <div class="detailed-progress">
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="progress-stat success">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ successCount }}</div>
                <div class="stat-label">成功</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="progress-stat error">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ errorCount }}</div>
                <div class="stat-label">失败</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="progress-stat processing">
              <div class="stat-icon">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ processingCount }}</div>
                <div class="stat-label">处理中</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 当前操作信息 -->
      <div v-if="currentOperation" class="current-operation">
        <div class="operation-info">
          <el-icon class="operation-icon"><Clock /></el-icon>
          <span class="operation-text">{{ currentOperation }}</span>
        </div>
        <div class="operation-time">
          <span class="time-label">已用时间:</span>
          <span class="time-value">{{ elapsedTime }}</span>
          <span v-if="estimatedTime" class="time-estimate">
            预计剩余: {{ estimatedTime }}
          </span>
        </div>
      </div>

      <!-- 操作步骤 -->
      <div v-if="showSteps" class="operation-steps">
        <div class="steps-title">
          <el-icon><List /></el-icon>
          <span>操作步骤</span>
        </div>
        <el-steps :active="currentStep" direction="vertical" size="small">
          <el-step 
            v-for="(step, index) in operationSteps" 
            :key="index"
            :title="step.title"
            :description="step.description"
            :status="step.status"
            :icon="step.icon"
          />
        </el-steps>
      </div>

      <!-- 错误信息 -->
      <div v-if="errors.length > 0" class="error-section">
        <div class="error-header">
          <el-icon class="error-icon"><WarningFilled /></el-icon>
          <span class="error-title">错误详情 ({{ errors.length }})</span>
          <el-button 
            size="small" 
            text 
            @click="showAllErrors = !showAllErrors"
          >
            {{ showAllErrors ? '收起' : '展开' }}
          </el-button>
        </div>
        <div v-show="showAllErrors" class="error-list">
          <div 
            v-for="(error, index) in displayErrors" 
            :key="index"
            class="error-item"
          >
            <div class="error-index">#{{ error.index + 1 }}</div>
            <div class="error-content">
              <div class="error-message">{{ error.message }}</div>
              <div v-if="error.details" class="error-details">
                {{ error.details }}
              </div>
            </div>
          </div>
          <div v-if="errors.length > maxDisplayErrors" class="error-more">
            还有 {{ errors.length - maxDisplayErrors }} 个错误...
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button 
          v-if="canPause && isProcessing" 
          :icon="VideoPause" 
          @click="handlePause"
        >
          暂停
        </el-button>
        <el-button 
          v-if="canResume && isPaused" 
          type="primary" 
          :icon="VideoPlay" 
          @click="handleResume"
        >
          继续
        </el-button>
        <el-button 
          v-if="canCancel && (isProcessing || isPaused)" 
          type="danger" 
          :icon="Close" 
          @click="handleCancel"
        >
          取消
        </el-button>
        <el-button 
          v-if="canRetry && (isCompleted || isError)" 
          type="warning" 
          :icon="RefreshRight" 
          @click="handleRetry"
        >
          重试失败项
        </el-button>
        <el-button 
          v-if="isCompleted" 
          :icon="Download" 
          @click="handleExportResult"
        >
          导出结果
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { 
  CircleCheck, 
  CircleClose, 
  Loading, 
  Clock, 
  List, 
  WarningFilled,
  VideoPause,
  VideoPlay,
  Close,
  RefreshRight,
  Download,
  Timer,
  Warning,
  SuccessFilled
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 操作类型
  operationType: {
    type: String,
    required: true
  },
  // 总数量
  totalCount: {
    type: Number,
    default: 0
  },
  // 已完成数量
  completedCount: {
    type: Number,
    default: 0
  },
  // 成功数量
  successCount: {
    type: Number,
    default: 0
  },
  // 错误数量
  errorCount: {
    type: Number,
    default: 0
  },
  // 当前操作描述
  currentOperation: {
    type: String,
    default: ''
  },
  // 错误列表
  errors: {
    type: Array,
    default: () => []
  },
  // 操作步骤
  operationSteps: {
    type: Array,
    default: () => []
  },
  // 当前步骤
  currentStep: {
    type: Number,
    default: 0
  },
  // 是否显示步骤
  showSteps: {
    type: Boolean,
    default: false
  },
  // 操作状态
  status: {
    type: String,
    default: 'idle', // idle, processing, paused, completed, error, cancelled
    validator: (value) => ['idle', 'processing', 'paused', 'completed', 'error', 'cancelled'].includes(value)
  },
  // 开始时间
  startTime: {
    type: Date,
    default: null
  },
  // 控制选项
  canPause: {
    type: Boolean,
    default: false
  },
  canResume: {
    type: Boolean,
    default: false
  },
  canCancel: {
    type: Boolean,
    default: true
  },
  canRetry: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits([
  'pause',
  'resume', 
  'cancel',
  'retry',
  'export-result'
])

// 响应式数据
const showAllErrors = ref(false)
const maxDisplayErrors = ref(5)
const elapsedTime = ref('00:00')
const estimatedTime = ref('')
const timer = ref(null)

// 计算属性
const progressPercentage = computed(() => {
  if (props.totalCount === 0) return 0
  return Math.round((props.completedCount / props.totalCount) * 100)
})

const processingCount = computed(() => {
  return props.totalCount - props.completedCount
})

const progressStatus = computed(() => {
  if (props.status === 'error') return 'exception'
  if (props.status === 'completed') return 'success'
  return undefined
})

const isProcessing = computed(() => props.status === 'processing')
const isPaused = computed(() => props.status === 'paused')
const isCompleted = computed(() => props.status === 'completed')
const isError = computed(() => props.status === 'error')

const operationTitle = computed(() => {
  const titles = {
    activate: '批量激活操作',
    deactivate: '批量停用操作',
    delete: '批量删除操作',
    copy: '批量复制操作',
    move: '批量移动操作',
    export: '批量导出操作',
    import: '批量导入操作'
  }
  return titles[props.operationType] || '批量操作'
})

const statusText = computed(() => {
  const texts = {
    idle: '准备中',
    processing: '处理中',
    paused: '已暂停',
    completed: '已完成',
    error: '出现错误',
    cancelled: '已取消'
  }
  return texts[props.status] || '未知状态'
})

const statusTagType = computed(() => {
  const types = {
    idle: 'info',
    processing: 'primary',
    paused: 'warning',
    completed: 'success',
    error: 'danger',
    cancelled: 'info'
  }
  return types[props.status] || 'info'
})

const statusIcon = computed(() => {
  const icons = {
    idle: Timer,
    processing: Loading,
    paused: VideoPause,
    completed: SuccessFilled,
    error: Warning,
    cancelled: Close
  }
  return icons[props.status] || Timer
})

const statusIconClass = computed(() => {
  return {
    'processing': isProcessing.value,
    'success': isCompleted.value,
    'error': isError.value
  }
})

const displayErrors = computed(() => {
  return props.errors.slice(0, maxDisplayErrors.value)
})

// 方法定义
const handlePause = () => {
  emit('pause')
}

const handleResume = () => {
  emit('resume')
}

const handleCancel = () => {
  emit('cancel')
}

const handleRetry = () => {
  emit('retry')
}

const handleExportResult = () => {
  emit('export-result')
}

const updateElapsedTime = () => {
  if (!props.startTime) return

  const now = new Date()
  const elapsed = now - props.startTime
  const minutes = Math.floor(elapsed / 60000)
  const seconds = Math.floor((elapsed % 60000) / 1000)
  elapsedTime.value = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`

  // 估算剩余时间
  if (props.completedCount > 0 && isProcessing.value) {
    const avgTimePerItem = elapsed / props.completedCount
    const remainingItems = props.totalCount - props.completedCount
    const estimatedMs = remainingItems * avgTimePerItem
    const estimatedMinutes = Math.floor(estimatedMs / 60000)
    const estimatedSeconds = Math.floor((estimatedMs % 60000) / 1000)
    estimatedTime.value = `${estimatedMinutes.toString().padStart(2, '0')}:${estimatedSeconds.toString().padStart(2, '0')}`
  }
}

// 监听器
watch(() => props.status, (newStatus) => {
  if (newStatus === 'processing' && !timer.value) {
    timer.value = setInterval(updateElapsedTime, 1000)
  } else if (newStatus !== 'processing' && timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
})

// 生命周期
onMounted(() => {
  if (isProcessing.value) {
    timer.value = setInterval(updateElapsedTime, 1000)
  }
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>

<style scoped>
.batch-progress-monitor {
  margin: 16px 0;
}

.progress-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 18px;
  transition: all 0.3s;
}

.header-icon.processing {
  color: #409eff;
  animation: spin 1s linear infinite;
}

.header-icon.success {
  color: #67c23a;
}

.header-icon.error {
  color: #f56c6c;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.header-title {
  font-weight: 600;
  color: #303133;
}

.overall-progress {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-weight: 500;
  color: #606266;
}

.progress-text {
  font-weight: 600;
  color: #303133;
}

.detailed-progress {
  margin-bottom: 20px;
}

.progress-stat {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  background: #fafbfc;
}

.progress-stat.success {
  border-color: #67c23a;
  background: #f0f9ff;
}

.progress-stat.error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.progress-stat.processing {
  border-color: #409eff;
  background: #ecf5ff;
}

.stat-icon {
  font-size: 24px;
}

.progress-stat.success .stat-icon {
  color: #67c23a;
}

.progress-stat.error .stat-icon {
  color: #f56c6c;
}

.progress-stat.processing .stat-icon {
  color: #409eff;
}

.stat-content {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.current-operation {
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.operation-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.operation-icon {
  color: #409eff;
}

.operation-text {
  font-weight: 500;
  color: #303133;
}

.operation-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #606266;
}

.time-label {
  color: #909399;
}

.time-value {
  font-weight: 600;
  color: #303133;
}

.time-estimate {
  color: #409eff;
}

.operation-steps {
  margin-bottom: 20px;
}

.steps-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: #606266;
}

.error-section {
  margin-bottom: 20px;
}

.error-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.error-icon {
  color: #f56c6c;
}

.error-title {
  font-weight: 500;
  color: #303133;
}

.error-list {
  max-height: 200px;
  overflow-y: auto;
}

.error-item {
  display: flex;
  gap: 12px;
  padding: 8px;
  margin-bottom: 8px;
  background: #fef0f0;
  border-radius: 4px;
  border-left: 3px solid #f56c6c;
}

.error-index {
  font-size: 12px;
  color: #f56c6c;
  font-weight: 600;
  min-width: 30px;
}

.error-content {
  flex: 1;
}

.error-message {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.error-details {
  font-size: 12px;
  color: #606266;
}

.error-more {
  text-align: center;
  padding: 8px;
  color: #909399;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progress-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .progress-info {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }

  .action-buttons {
    flex-direction: column;
  }

  .operation-time {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }
}
</style>
